import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import App from './App';
import { RefreshProvider } from './contexts/RefreshContext';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <RefreshProvider>
        <App />
      </RefreshProvider>
    </ThemeProvider>
  </React.StrictMode>,
) 