import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
} from '@mui/material';
import InsightsIcon from '@mui/icons-material/Insights';
import CloseIcon from '@mui/icons-material/Close';
import Chart from '../charts/Chart';

const FuturePredictionsDialog = ({
  open,
  onClose,
  symbol,
  isLoading,
  error,
  data,
}) => {
  if (!open) return null;

  let content;
  if (isLoading) {
    content = (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300, width: '100%' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>正在加载 {symbol} 的未来价格预测...</Typography>
      </Box>
    );
  } else if (error) {
    content = (
      <Alert severity="error" sx={{ my: 2, width: '100%' }}>
        {error}
      </Alert>
    );
  } else if (data) {
    const hasPlot = data.plot_image_base64 && typeof data.plot_image_base64 === 'string' && data.plot_image_base64.trim() !== '';
    const hasTableData = data.future_predictions_data && Array.isArray(data.future_predictions_data) && data.future_predictions_data.length > 0;

    if (hasPlot || hasTableData) {
      content = (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%', p: 1 }}>
          {hasTableData && (
            <Box sx={{ width: '100%', mb: hasPlot ? 2 : 0, mt: 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  overflowX: 'auto',
                  p: 1,
                  gap: 1.5,
                  bgcolor: theme => theme.palette.mode === 'dark' ? 'grey.800' : 'grey.100',
                  border: theme => `1px solid ${theme.palette.divider}`,
                  borderRadius: '8px',
                  minHeight: 70,
                  alignItems: 'center',
                }}
              >
                {data.future_predictions_data.map((pred, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      p: 1,
                      minWidth: 100,
                      height: '100%',
                      bgcolor: 'background.paper',
                      border: theme => `1px solid ${theme.palette.divider}`,
                      borderRadius: '4px',
                      boxShadow: 1,
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'text.secondary',
                        whiteSpace: 'nowrap',
                        mb: 0.5,
                        fontWeight: 500
                      }}
                    >
                      {pred.date ? new Date(pred.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' }) : `预测 ${index + 1}`}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main', whiteSpace: 'nowrap' }}>
                      {typeof pred.price === 'number' ? `$${pred.price.toFixed(2)}` : 'N/A'}
                    </Typography>
                    <Typography variant="caption" sx={{ color: 'text.secondary', whiteSpace: 'nowrap', mt: 0.25 }}>
                      {
                        typeof pred.lower_bound === 'number' && typeof pred.upper_bound === 'number'
                          ? `(${pred.lower_bound.toFixed(2)} - ${pred.upper_bound.toFixed(2)})`
                          : ''
                      }
                    </Typography>
                  </Box>
                ))}
                {data.future_predictions_data.length === 0 && (
                   <Typography variant="caption" sx={{ p: 2, textAlign: 'center', width: '100%', color: 'text.secondary' }}>
                     暂无详细每日预测数据。
                   </Typography>
                )}
              </Box>
            </Box>
          )}
          {hasPlot && (
            <Box sx={{ width: '100%', textAlign: 'center', mt: 2 }}>
              <img
                src={`data:image/png;base64,${data.plot_image_base64}`}
                alt={`${symbol} Prediction Plot`}
                style={{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd', borderRadius: '4px', display: 'block', margin: '0 auto' }}
              />
            </Box>
          )}
        </Box>
      );
    } else {
      // Data is present, but plot is null/empty and future_predictions_data is empty
      content = (
        <Alert severity="info" sx={{ my: 2, width: '100%' }}>
          模型已成功运行，但没有可供显示的预测图或详细数据点。
        </Alert>
      );
    }
  } else {
    // Data is null or undefined (e.g. initial state before data is fetched, or explicit error in fetching)
    content = (
      <Alert severity="warning" sx={{ my: 2, width: '100%' }}>
        无法加载预测数据。如果问题持续，请稍后再试。
      </Alert>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InsightsIcon sx={{ mr: 1, color: 'secondary.main' }} />
            未来价格预测: {symbol}
          </Box>
          <IconButton onClick={onClose} size="small"><CloseIcon /></IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        {content}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

export default FuturePredictionsDialog; 