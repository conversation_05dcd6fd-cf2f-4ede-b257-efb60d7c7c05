import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Alert,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { toast } from 'react-hot-toast';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';

const WatchlistDialog = ({ open, onClose, selectedAccount, onWatchlistsUpdated }) => {
  const [watchlists, setWatchlists] = useState([]);
  const [editingWatchlist, setEditingWatchlist] = useState(null);
  const [watchlistName, setWatchlistName] = useState('');
  const [selectedSymbols, setSelectedSymbols] = useState([]);
  const [symbolInput, setSymbolInput] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(false);



  useEffect(() => {
    if (open && selectedAccount) {
      loadWatchlists();
    }
  }, [open, selectedAccount]);

  const loadWatchlists = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_WATCHLISTS}?account_id=${selectedAccount}`
      );
      const data = await response.json();
      
      if (response.ok) {
        setWatchlists(data.watchlists || []);
      } else {
        toast.error(`加载观察列表失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`加载观察列表失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    setIsCreating(true);
    setEditingWatchlist(null);
    setWatchlistName('');
    setSelectedSymbols([]);
  };

  const handleEdit = (watchlist) => {
    setIsCreating(true);
    setEditingWatchlist(watchlist);
    setWatchlistName(watchlist.name);
    setSelectedSymbols(watchlist.symbols || []);
  };

  const handleSave = async () => {
    if (!watchlistName.trim()) {
      toast.error('请输入观察列表名称');
      return;
    }

    if (selectedSymbols.length === 0) {
      toast.error('请至少添加一个股票代码');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.OPTIONS_WATCHLISTS}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_id: selectedAccount,
          name: watchlistName.trim(),
          symbols: selectedSymbols,
          watchlist_id: editingWatchlist?.watchlist_id,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(editingWatchlist ? '观察列表已更新' : '观察列表已创建');
        setIsCreating(false);
        setEditingWatchlist(null);
        await loadWatchlists();
        onWatchlistsUpdated && onWatchlistsUpdated();
      } else {
        toast.error(`保存失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`保存失败: ${error.message}`);
    }
  };

  const handleDelete = async (watchlistId) => {
    if (!window.confirm('确定要删除这个观察列表吗？')) {
      return;
    }

    try {
      // Note: You'll need to implement a DELETE endpoint in the backend
      toast.info('删除功能待实现');
    } catch (error) {
      toast.error(`删除失败: ${error.message}`);
    }
  };

  const handleAddSymbol = () => {
    const input = symbolInput.trim();
    if (!input) return;

    // Parse symbols using multiple separators: comma, semicolon, and whitespace
    // Use regex to split on comma, semicolon, or one or more whitespace characters
    const symbols = input
      .split(/[,;\s]+/)
      .map(symbol => symbol.trim().toUpperCase())
      .filter(symbol => symbol.length > 0); // Remove empty strings

    // Add only new symbols that aren't already in the list
    const newSymbols = symbols.filter(symbol => !selectedSymbols.includes(symbol));

    if (newSymbols.length > 0) {
      setSelectedSymbols([...selectedSymbols, ...newSymbols]);
      setSymbolInput('');

      // Show feedback to user
      if (newSymbols.length === 1) {
        toast.success(`已添加股票: ${newSymbols[0]}`);
      } else {
        toast.success(`已添加 ${newSymbols.length} 只股票: ${newSymbols.join(', ')}`);
      }
    } else if (symbols.length > 0) {
      // All symbols were duplicates
      toast.info('所有股票代码已存在于列表中');
    }
  };

  const handleRemoveSymbol = (symbolToRemove) => {
    setSelectedSymbols(selectedSymbols.filter(symbol => symbol !== symbolToRemove));
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingWatchlist(null);
    setWatchlistName('');
    setSelectedSymbols([]);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {isCreating ? (editingWatchlist ? '编辑观察列表' : '创建观察列表') : '管理观察列表'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {!isCreating ? (
          // List view
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">现有观察列表</Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={handleCreateNew}
                variant="contained"
                size="small"
              >
                新建列表
              </Button>
            </Box>

            {loading ? (
              <Typography>加载中...</Typography>
            ) : watchlists.length === 0 ? (
              <Alert severity="info">
                暂无观察列表，点击"新建列表"创建第一个观察列表
              </Alert>
            ) : (
              <List>
                {watchlists.map((watchlist, index) => (
                  <React.Fragment key={watchlist.watchlist_id}>
                    <ListItem>
                      <ListItemText
                        primary={watchlist.name}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {watchlist.symbols.length} 只股票
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                              {watchlist.symbols.slice(0, 10).map((symbol) => (
                                <Chip key={symbol} label={symbol} size="small" />
                              ))}
                              {watchlist.symbols.length > 10 && (
                                <Chip label={`+${watchlist.symbols.length - 10} 更多`} size="small" variant="outlined" />
                              )}
                            </Box>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton onClick={() => handleEdit(watchlist)} size="small">
                          <EditIcon />
                        </IconButton>
                        <IconButton onClick={() => handleDelete(watchlist.watchlist_id)} size="small">
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < watchlists.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Box>
        ) : (
          // Create/Edit form
          <Box>
            <TextField
              fullWidth
              label="观察列表名称"
              value={watchlistName}
              onChange={(e) => setWatchlistName(e.target.value)}
              sx={{ mb: 3 }}
            />

            <Typography variant="subtitle2" gutterBottom>
              添加股票代码
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                fullWidth
                label="股票代码"
                placeholder="输入股票代码，如 AAPL"
                value={symbolInput}
                onChange={(e) => setSymbolInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddSymbol();
                  }
                }}
                sx={{ flexGrow: 1 }}
              />
              <Button
                onClick={handleAddSymbol}
                variant="outlined"
                disabled={!symbolInput.trim()}
              >
                添加
              </Button>
            </Box>

            <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
              💡 提示：可以输入多个股票代码，支持多种分隔符
            </Typography>

            {selectedSymbols.length > 0 && (
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  已选择的股票 ({selectedSymbols.length})
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {selectedSymbols.map((symbol) => (
                    <Chip
                      key={symbol}
                      label={symbol}
                      onDelete={() => handleRemoveSymbol(symbol)}
                      size="small"
                    />
                  ))}
                </Box>
              </Paper>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        {isCreating ? (
          <>
            <Button onClick={handleCancel}>取消</Button>
            <Button onClick={handleSave} variant="contained">
              {editingWatchlist ? '更新' : '创建'}
            </Button>
          </>
        ) : (
          <Button onClick={onClose}>关闭</Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default WatchlistDialog;
