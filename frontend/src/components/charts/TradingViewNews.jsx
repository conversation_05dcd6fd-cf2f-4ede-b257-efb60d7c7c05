import React, { useEffect, useRef, memo } from 'react';

function TradingViewNews() {
  const container = useRef();

  useEffect(() => {
    if (container.current) {
        container.current.innerHTML = '';
    }

    const script = document.createElement("script");
    script.src = "https://s3.tradingview.com/external-embedding/embed-widget-timeline.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = JSON.stringify({
        "feedMode": "market",
        "isTransparent": false,
        "displayMode": "adaptive",
        "width": "100%",
        "height": "100%",
        "colorTheme": "light",
        "locale": "en"
      });
    
    if (container.current) {
      container.current.appendChild(script);
    }
  }, []);

  return (
    <div className="tradingview-widget-container" style={{ height: "500px", width: "100%"}}>
      <div className="tradingview-widget-container__widget" ref={container} style={{ height: "100%", width: "100%" }}></div>
      <div className="tradingview-widget-copyright">
        <a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank">
          <span className="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    </div>
  );
}

export default memo(TradingViewNews); 