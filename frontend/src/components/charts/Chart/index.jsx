import React from 'react';
import ReactECharts from 'echarts-for-react';
import { useTheme } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';

// Helper to process input series, prioritizing existing styles
const processInputSeries = (inputSeries, theme) => {
  if (!Array.isArray(inputSeries)) {
    console.warn('[Chart processInputSeries] Received non-array series:', inputSeries);
    return [];
  }
  return inputSeries.reduce((acc, s, index) => {
    if (!s || !s.name || !Array.isArray(s.data)) {
      console.warn(`[Chart processInputSeries] Skipping invalid series at index ${index}:`, s);
      return acc;
    }
    const processedData = (s.data || [])
      .map(item => [item?.[0], parseFloat(item?.[1])])
      .filter(item => item[0] != null && item[1] !== null && !isNaN(item[1]));

    acc.push({
      name: s.name,
      type: s.type || 'line',
      yAxisIndex: s.yAxisIndex || 0,
      data: processedData,
      smooth: s.smooth !== undefined ? s.smooth : true, // Prioritize incoming smooth, default true
      showSymbol: s.showSymbol !== undefined ? s.showSymbol : false, // Prioritize incoming showSymbol, default false
      lineStyle: s.lineStyle || {
        width: s.name === '收盘价' || s.name === 'RSI' ? 2 : 1,
        type: s.name === 'RSI' ? 'dotted' : 'solid'
      },
      itemStyle: s.itemStyle || {
        color: getColor(s.name, theme.palette) // Fallback to getColor if no itemStyle provided
      },
      areaStyle: s.name === 'RSI' ? (s.areaStyle || {
        color: {
          type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{ offset: 0, color: theme.palette.warning.main + '40' }, { offset: 1, color: theme.palette.warning.main + '00' }]
        }
      }) : undefined,
      markLine: s.name === 'RSI' ? (s.markLine || {
        silent: true,
        data: [
          { yAxis: 70, name: '超买', label: { show: true, formatter: '超买 (70)', position: 'insideEndTop', backgroundColor: theme.palette.background.paper, padding: [2, 4], borderRadius: 2 }, lineStyle: { color: theme.palette.error.main, type: 'dashed', width: 1.5 } },
          { yAxis: 30, name: '超卖', label: { show: true, formatter: '超卖 (30)', position: 'insideEndBottom', backgroundColor: theme.palette.background.paper, padding: [2, 4], borderRadius: 2 }, lineStyle: { color: theme.palette.success.main, type: 'dashed', width: 1.5 } }
        ]
      }) : undefined,
      markPoint: s.markPoint, // Directly use incoming markPoint if provided
    });
    return acc;
  }, []);
};

// Helper to construct default chart infrastructure (axes, grid, legend, etc.)
const constructDefaultInfrastructure = (processedSeries, theme, options = {}) => {
  const hasRsiSeries = processedSeries.some(s => s.name && s.name.includes('RSI'));
  const hasMacdSeries = processedSeries.some(s => s.name && s.name.includes('MACD'));

  // --- 动态计算价格Y轴范围 ---
  let priceMin = null;
  let priceMax = null;
  
  // 找到价格相关的系列数据
  const priceSeries = processedSeries.filter(s => 
    s.yAxisIndex === 0 || s.yAxisIndex === undefined
  );
  
  if (priceSeries.length > 0) {
    const allPriceValues = [];
    priceSeries.forEach(series => {
      if (Array.isArray(series.data)) {
        series.data.forEach(point => {
          if (point && point.length > 1 && typeof point[1] === 'number' && !isNaN(point[1])) {
            allPriceValues.push(point[1]);
          }
        });
      }
    });
    
    if (allPriceValues.length > 0) {
      priceMin = Math.min(...allPriceValues);
      priceMax = Math.max(...allPriceValues);
      
      // 添加3%的缓冲区使图表更美观（减少缓冲区让波动更明显）
      const range = priceMax - priceMin;
      const buffer = range * 0.03;
      priceMin = Math.max(0, priceMin - buffer); // 确保最小值不小于0
      priceMax = priceMax + buffer;
    }
  }

  const yAxes = [
    {
      type: 'value',
      name: '价格', // Default Y-axis name
      position: 'left',
      ...(priceMin !== null && priceMax !== null ? { min: priceMin, max: priceMax } : {}), // 只有在有计算值时才设置
      nameTextStyle: { padding: [0, 0, 0, 10], color: theme.palette.text.primary },
      axisLabel: { color: theme.palette.text.secondary, formatter: (value) => value.toFixed(2) },
      splitLine: { show: true, lineStyle: { type: 'dashed', color: theme.palette.divider } }
    }
  ];
  if (hasRsiSeries) {
    yAxes.push({
      type: 'value', name: 'RSI/SSO', position: 'right',
      nameTextStyle: { padding: [0, 0, 0, 10], color: theme.palette.warning.main, fontWeight: 'bold' },
      axisLabel: { color: theme.palette.warning.main, formatter: (value) => value.toFixed(1) },
      splitLine: { show: true, lineStyle: { type: 'dotted', color: theme.palette.divider, opacity: 0.5 } }
    });
  }
  
  let rightGrid = 60;
  if (hasRsiSeries) rightGrid += 50;

  return {
    grid: { top: 40, right: rightGrid, bottom: 100, left: 60, containLabel: true },
    xAxis: {
      type: 'time',
      axisLabel: { color: theme.palette.text.secondary, formatter: (value) => { const date = new Date(value); return `${date.getMonth()+1}/${date.getDate()}`; }, rotate: 30, margin: 14 },
      splitLine: { show: true, lineStyle: { type: 'dashed', color: theme.palette.divider } }
    },
    yAxis: yAxes,
    tooltip: {
      trigger: 'axis', axisPointer: { type: 'cross', label: { backgroundColor: theme.palette.primary.main } },
      formatter: (params) => {
        if (!params || params.length === 0) return '';
        const date = new Date(params[0].value[0]);
        const dateStr = `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        let result = `<div style="font-size: 12px; line-height: 1.6;"><strong>${dateStr}</strong><br/>`;

        // Group params by seriesName to handle potential aggregation
        const seriesGroups = {};
        params.forEach(param => {
          if (param.value && param.value[1] !== null && param.value[1] !== undefined) {
            if (!seriesGroups[param.seriesName]) {
              seriesGroups[param.seriesName] = {
                values: [],
                color: param.color, // Store color from the first instance
                // marker: param.marker // ECharts marker is simple HTML, can reconstruct
              };
            }
            seriesGroups[param.seriesName].values.push(param.value[1]);
          }
        });

        // Helper to format tooltip value
        const formatValue = (seriesName, value) => {
          if (seriesName.includes('RSI') || seriesName.includes('MACD') || seriesName === 'Signal') {
            return value.toFixed(2);
          }
          return `$${value.toFixed(2)}`;
        };

        // Define the desired order for series in the tooltip
        const seriesOrder = ['历史实际数据', '评估实际数据', '评估预测数据', '未来预测数据', '股价', '收盘价', '20日均线', '50日均线', 'RSI指标', 'RSI', 'MACD柱状图', 'MACD', 'Signal', 'MACD Histogram'];
        
        seriesOrder.forEach(seriesName => {
          if (seriesGroups[seriesName]) {
            const group = seriesGroups[seriesName];
            const values = group.values;
            const color = group.color || getColor(seriesName, theme.palette);
            const markerHtml = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;

            if ((seriesName === '评估实际数据' || seriesName === '评估预测数据') && values.length > 0) {
              const sum = values.reduce((acc, val) => acc + val, 0);
              const avg = sum / values.length;
              result += `${markerHtml}${seriesName}: <strong>$${avg.toFixed(2)}</strong><br/>`;
            } else {
              // For other series, or if only one value for target series, list them
              values.forEach(value => {
                result += `${markerHtml}${seriesName}: <strong>${formatValue(seriesName, value)}</strong><br/>`;
              });
            }
          }
        });

        // Handle any series not in the predefined order (e.g., dynamically added)
        Object.keys(seriesGroups).forEach(seriesName => {
          if (!seriesOrder.includes(seriesName)) {
            const group = seriesGroups[seriesName];
            const values = group.values;
            const color = group.color || getColor(seriesName, theme.palette);
            const markerHtml = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            values.forEach(value => {
              result += `${markerHtml}${seriesName}: <strong>${formatValue(seriesName, value)}</strong><br/>`;
            });
          }
        });

        result += `</div>`;
        return result;
      },
      backgroundColor: alpha(theme.palette.background.paper, 0.9), borderColor: theme.palette.divider, textStyle: { color: theme.palette.text.primary }
    },
    legend: {
      data: processedSeries.map(s => s.name),
      selectedMode: 'multiple', textStyle: { color: theme.palette.text.primary },
      bottom: 10, padding: [5, 10], itemGap: 15,
      selected: { 'RSI': true } // Default selected state for RSI if present
    },
    dataZoom: [
      { type: 'inside', start: 30, end: 100, minValueSpan: 3600 * 24 * 1000 * 5 },
      { type: 'slider', start: 30, end: 100, bottom: 50, height: 20, borderColor: theme.palette.divider, textStyle: { color: theme.palette.text.secondary } }
    ]
  };
};

export default function Chart({ series: inputSeries, options: optionOverrides, height = 600 }) {
  const theme = useTheme();
  
  const getFinalOption = () => {
    // 1. Process inputSeries to get styled series, prioritizing styles from inputSeries itself
    const echartsStyledSeries = processInputSeries(inputSeries, theme);

    // 2. Construct default infrastructure (grid, axes, tooltip, legend, dataZoom)
    //    This uses the processed series to determine things like legend data and RSI axis presence.
    const defaultOptions = constructDefaultInfrastructure(echartsStyledSeries, theme, optionOverrides);

    // 3. Merge optionOverrides with defaultOptions.
    //    optionOverrides from parent component will take precedence.
    //    The series array will be the one from echartsStyledSeries unless optionOverrides provides its own series array.
    const finalOptions = {
      ...defaultOptions, // Base structure and styling
      ...(optionOverrides || {}), // Parent component's overrides for title, legend, axes, grid, etc.
      series: optionOverrides?.series || echartsStyledSeries, // Prioritize series from overrides if provided
    };
    
    // Ensure title, legend, xAxis, yAxis, grid, tooltip, dataZoom are properly merged if provided in optionOverrides
    // For complex objects/arrays (like yAxis), a simple spread might not be enough for deep merging if only partial overrides are given.
    // ECharts handles merging when setOption is called, but we construct the initial full object here.
    // If optionOverrides.yAxis exists, it will replace defaultOptions.yAxis due to the spread order.
    // This is often desired if the parent provides a full axis definition.
    if (optionOverrides && optionOverrides.title) finalOptions.title = {...(defaultOptions.title || {}), ...optionOverrides.title};
    if (optionOverrides && optionOverrides.legend) finalOptions.legend = {...defaultOptions.legend, ...optionOverrides.legend};
    if (optionOverrides && optionOverrides.grid) finalOptions.grid = {...defaultOptions.grid, ...optionOverrides.grid};
    if (optionOverrides && optionOverrides.tooltip) finalOptions.tooltip = {...defaultOptions.tooltip, ...optionOverrides.tooltip};
    if (optionOverrides && optionOverrides.xAxis) finalOptions.xAxis = Array.isArray(optionOverrides.xAxis) ? optionOverrides.xAxis.map((ax, i) => ({...(defaultOptions.xAxis[i] || defaultOptions.xAxis || {}), ...ax})) : {...(defaultOptions.xAxis || {}), ...optionOverrides.xAxis};
    if (optionOverrides && optionOverrides.yAxis) finalOptions.yAxis = Array.isArray(optionOverrides.yAxis) ? optionOverrides.yAxis.map((ax, i) => ({...(defaultOptions.yAxis[i] || {}), ...ax})) : {...(defaultOptions.yAxis || {}), ...optionOverrides.yAxis};
    if (optionOverrides && optionOverrides.dataZoom) finalOptions.dataZoom = Array.isArray(optionOverrides.dataZoom) ? optionOverrides.dataZoom.map((dz, i) => ({...(defaultOptions.dataZoom[i] || {}), ...dz})) : {...(defaultOptions.dataZoom || {}), ...optionOverrides.dataZoom};

    // Check for no valid data after all processing
    const finalSeriesForCheck = finalOptions.series || [];
    const hasValidData = finalSeriesForCheck.some(s => s && Array.isArray(s.data) && s.data.length > 0);
    if (!hasValidData) {
      console.warn('[Chart getFinalOption] No valid data points in final series.');
      return {
        title: { text: '暂无有效数据点', textStyle: { color: theme.palette.text.primary }, left: 'center', top: 'center' }
      };
    }
    
    console.log('[Chart getFinalOption] Final ECharts options:', JSON.stringify(finalOptions));
    return finalOptions;
  };

  return <ReactECharts 
    option={getFinalOption()} 
    style={{ height, width: '100%' }} 
    theme={theme.palette.mode}
    notMerge={true} // Important: ensure options replace, not merge internally by ECharts instance on re-render
  />;
}

// Color mapping function (remains the same)
const getColor = (name, palette) => {
  const colors = {
    '收盘价': palette.primary.main,
    '股价': palette.primary.main,
    '20日均线': palette.secondary.main,
    '50日均线': palette.info.main,
    'RSI': palette.warning.main,
    'RSI指标': palette.warning.main,
    'MACD': '#E0B400',
    'MACD柱状图': palette.success.main,
    'Signal': '#4682B4',
    // MACD Histogram color is handled by a function in the series definition
    // Add other series names if they need default colors by name
  };
  return colors[name] || palette.text.secondary; // Default color for unspecified series
}; 