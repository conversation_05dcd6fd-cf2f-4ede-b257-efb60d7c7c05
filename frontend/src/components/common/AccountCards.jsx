import React, { useState, useEffect, useContext } from 'react';
import { styled } from '@mui/material/styles';
import {
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Card,
  CardContent,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import axios from 'axios';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import { RefreshContext } from '../../contexts/RefreshContext';

const AccountCardsContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  width: '100%',
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  '&:last-child': {
    marginBottom: 0,
  },
}));

const AccountSummary = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  width: '100%',
}));

const ReturnValue = styled(Typography)(({ positive }) => ({
  color: positive ? 'green' : 'red',
}));

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  '&:hover': {
    boxShadow: theme.shadows[4],
  },
}));

const DeleteButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  right: theme.spacing(1),
  top: theme.spacing(1),
}));

const AddCapitalButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  right: theme.spacing(6),
  top: theme.spacing(1),
}));

function AccountCards() {
  const [accounts, setAccounts] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [amount, setAmount] = useState('');
  const [error, setError] = useState('');
  const [newAccountName, setNewAccountName] = useState('');
  const [initialCapital, setInitialCapital] = useState('');
  const { refreshFlag, setRefreshFlag } = useContext(RefreshContext);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.OVERVIEW}`);
        const data = await response.json();
        setAccounts(data.account_breakdown || []);
      } catch (error) {
        console.error('Error fetching accounts:', error);
      }
    };

    fetchAccounts();
  }, [refreshFlag]);

  const handleDelete = async (accountId, event) => {
    event?.stopPropagation();
    if (!accountId || isNaN(accountId)) {
      console.error('Invalid account ID:', accountId);
      alert('无效的账户ID');
      return;
    }
    
    if (!window.confirm('确定要删除这个账户吗？')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.ACCOUNTS}/${accountId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`删除失败: ${response.status}`);
      }

      // 刷新账户列表
      setRefreshFlag(prev => !prev);
    } catch (error) {
      console.error('Error deleting account:', error);
      alert('删除账户失败，请重试');
    }
  };

  const handleOpenDialog = (account) => {
    setSelectedAccount(account);
    setOpenDialog(true);
    setAmount('');
    setError('');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAccount(null);
    setAmount('');
    setError('');
  };

  const handleOpenCreateDialog = () => {
    setOpenCreateDialog(true);
    setNewAccountName('');
    setInitialCapital('');
    setError('');
  };

  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
    setNewAccountName('');
    setInitialCapital('');
    setError('');
  };

  const handleCreateAccount = async () => {
    if (!newAccountName.trim()) {
      setError('请输入账户名称');
      return;
    }

    if (!initialCapital || isNaN(initialCapital) || parseFloat(initialCapital) < 0) {
      setError('请输入有效的初始资金');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.ACCOUNTS}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newAccountName.trim(),
          initial_capital: parseFloat(initialCapital)
        })
      });

      if (!response.ok) {
        throw new Error(`创建失败: ${response.status}`);
      }

      const newAccount = await response.json();
      console.log('Created account:', newAccount);
      
      // 刷新账户列表
      setRefreshFlag(prev => !prev);
      
      handleCloseCreateDialog();
    } catch (error) {
      console.error('Error creating account:', error);
      setError('创建账户失败，请重试');
    }
  };

  const handleAddCapital = async () => {
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
      setError('请输入有效的金额');
      return;
    }

    try {
      await axios.post(`${API_BASE_URL}${API_ENDPOINTS.TRANSACTIONS}`, {
        account_id: selectedAccount.account_id,
        symbol: 'CASH',
        quantity: parseFloat(amount),
        price: 1,
        transaction_type: 'DEPOSIT'
      });

      // 刷新账户数据
      setRefreshFlag(prev => !prev);
      
      handleCloseDialog();
    } catch (error) {
      console.error('Error adding capital:', error);
      setError('添加本金失败，请重试');
    }
  };

  return (
    <>
      <AccountCardsContainer elevation={2}>
        <Typography variant="h6" gutterBottom>
          Account Details
        </Typography>
        {accounts.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4
          }}>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              暂无账户
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
            >
              创建账户
            </Button>
          </Box>
        ) : (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleOpenCreateDialog}
              >
                创建账户
              </Button>
            </Box>
            {accounts.map((account) => (
              <StyledAccordion key={account.account_id}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <AccountSummary>
                    <Typography variant="subtitle1">
                      {account.name}
                    </Typography>
                    <Box>
                      <ReturnValue variant="subtitle1" positive={account.return_rate >= 0}>
                        {(account.return_rate * 100).toFixed(2)}%
                      </ReturnValue>
                      <Typography variant="body2" color="text.secondary">
                        ${account.current_value.toLocaleString()}
                      </Typography>
                    </Box>
                  </AccountSummary>
                </AccordionSummary>
                <AccordionDetails>
                  <DeleteButton
                    size="small"
                    onClick={(event) => handleDelete(account.account_id, event)}
                  >
                    <DeleteIcon />
                  </DeleteButton>
                  <AddCapitalButton
                    size="small"
                    onClick={() => handleOpenDialog(account)}
                  >
                    <MonetizationOnIcon />
                  </AddCapitalButton>
                </AccordionDetails>
              </StyledAccordion>
            ))}
          </>
        )}
      </AccountCardsContainer>


      {/* 创建账户对话框 */}
      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>
        <DialogTitle>创建新账户</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              autoFocus
              margin="dense"
              label="账户名称"
              type="text"
              fullWidth
              value={newAccountName}
              onChange={(e) => setNewAccountName(e.target.value)}
              error={!!error && !newAccountName}
              helperText={error && !newAccountName ? error : ''}
            />
            <TextField
              margin="dense"
              label="初始资金"
              type="number"
              fullWidth
              value={initialCapital}
              onChange={(e) => setInitialCapital(e.target.value)}
              error={!!error && !initialCapital}
              helperText={error && !initialCapital ? error : ''}
              InputProps={{
                startAdornment: <Typography>$</Typography>,
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateDialog}>取消</Button>
          <Button onClick={handleCreateAccount} variant="contained">
            创建
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default AccountCards; 