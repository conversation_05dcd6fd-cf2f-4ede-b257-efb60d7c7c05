import React, { useState, useEffect } from 'react';
import {
  Card<PERSON>ontent,
  Typography,
  CircularProgress,
  Box,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import {
  PageCard,
  GlassCard,
  SecondaryButton,
  ModernDialog,
  ModernDialogTitle,
  ModernTableContainer,
  ModernTableHeadCell,
  ModernTableRow,
  ValueCell,
  LoadingBox,
  StyledAlert,
  EmptyStateBox,
  GradientTypography,
} from '../../styles/common';

function AccountDetails({ refreshKey }) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [realizedGains, setRealizedGains] = useState([]);
  const [loadingGains, setLoadingGains] = useState(false);
  const [gainsError, setGainsError] = useState(null);

  // Temporary states for dialog content to persist during exit animation
  const [dialogAccount, setDialogAccount] = useState(null);
  const [dialogGains, setDialogGains] = useState([]);
  const [dialogLoadingGains, setDialogLoadingGains] = useState(false);
  const [dialogGainsError, setDialogGainsError] = useState(null);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.OVERVIEW}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setAccounts(data.account_breakdown || []);
        setError(null);
      } catch (error) {
        console.error('Error fetching accounts:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAccounts();
  }, [refreshKey]);

  const handleShowRealizedGains = async (accountId, accountName) => {
    const account = { id: accountId, name: accountName };
    setSelectedAccount(account); // Keep this for immediate UI updates if needed elsewhere
    setDialogAccount(account);   // Set dialog-specific data

    setLoadingGains(true);
    setDialogLoadingGains(true); // Set dialog-specific loading

    setGainsError(null);
    setDialogGainsError(null);   // Clear dialog-specific error

    setRealizedGains([]);      // Clear previous gains for the main state
    setDialogGains([]);        // Clear previous gains for the dialog state
    
    setDialogOpen(true);

    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.REALIZED_GAINS}?account_id=${accountId}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setRealizedGains(data.realized_gains || []); // Update main state
      setDialogGains(data.realized_gains || []);   // Update dialog state
    } catch (error) {
      console.error('Error fetching realized gains:', error);
      setGainsError(error.message);           // Update main state
      setDialogGainsError(error.message);     // Update dialog state
    } finally {
      setLoadingGains(false);                 // Update main state
      setDialogLoadingGains(false);           // Update dialog state
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    // Data will be cleared by handleDialogExited
  };

  const handleDialogExited = () => {
    setSelectedAccount(null);
    setRealizedGains([]);
    setGainsError(null);
    setLoadingGains(false); // Reset loading state as well

    // Clear dialog specific states
    setDialogAccount(null);
    setDialogGains([]);
    setDialogGainsError(null);
    setDialogLoadingGains(false);
  };

  if (loading) {
    return (
      <PageCard>
        <CardContent>
          <LoadingBox>
            <CircularProgress sx={{ color: '#1976D2' }} />
          </LoadingBox>
        </CardContent>
      </PageCard>
    );
  }

  if (error) {
    return (
      <PageCard>
        <CardContent sx={{ p: 3 }}>
          <StyledAlert severity="error">加载账户数据时出错: {error}</StyledAlert>
        </CardContent>
      </PageCard>
    );
  }

  if (!accounts || accounts.length === 0) {
    return (
      <PageCard>
        <CardContent>
          <EmptyStateBox>
            <Typography 
              variant="body1" 
              sx={{
                color: '#64748B',
                fontWeight: '500',
                textAlign: 'center'
              }}
            >
              暂无账户数据
            </Typography>
          </EmptyStateBox>
        </CardContent>
      </PageCard>
    );
  }

  return (
    <PageCard>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <GradientTypography>
            账户详情
          </GradientTypography> 
        </Box>
        {accounts.map((account, index) => (
          <GlassCard 
            key={account.account_id}
            sx={{
              animation: `slideInAccount 0.5s ease-out ${index * 0.1}s both`,
              '@keyframes slideInAccount': {
                '0%': {
                  opacity: 0,
                  transform: 'translateY(20px)'
                },
                '100%': {
                  opacity: 1,
                  transform: 'translateY(0)'
                }
              }
            }}
          >
            <Typography 
              variant="subtitle1"
              sx={{
                fontWeight: '700',
                fontSize: '1.1rem',
                background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1.5
              }}
            >
              {account.name}
            </Typography>
            <Typography 
              variant="body2" 
              sx={{ 
                color: '#64748B', 
                fontWeight: '500',
                mb: 0.5
              }}
            >
              当前市值: <Box component="span" sx={{ fontWeight: '600', color: '#1976D2' }}>
                ${account.current_value?.toLocaleString() || '0'}
              </Box>
            </Typography>
            <Typography 
              variant="body2" 
              sx={{ 
                color: '#64748B', 
                fontWeight: '500',
                mb: 0.5
              }}
            >
              已实现收益: <Box 
                component="span" 
                sx={{ 
                  fontWeight: '600',
                  color: (account.realized_gains || 0) >= 0 ? '#2E7D32' : '#C62828'
                }}
              >
                ${account.realized_gains?.toLocaleString() || '0'}
              </Box>
            </Typography>
            <Typography
              variant="body2"
              sx={{
                fontWeight: '600',
                color: (account.return_rate || 0) >= 0 ? '#2E7D32' : '#C62828',
                mb: 1.5
              }}
            >
              收益率: {((account.return_rate || 0) * 100).toFixed(2)}%
            </Typography>
            <SecondaryButton 
              variant="outlined"
              size="small"
              onClick={() => handleShowRealizedGains(account.account_id, account.name)}
              sx={{ mt: 1.5, padding: '8px 16px' }}
            >
              查看已实现收益详情
            </SecondaryButton>
          </GlassCard>
        ))}
      </CardContent>

      <ModernDialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        TransitionProps={{ onExited: handleDialogExited }}
        fullWidth
        maxWidth="md"
      >
        <ModernDialogTitle>
          {dialogAccount?.name} - 已实现收益详情
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '#1976D2',
              transition: 'all 0.2s ease',
              '&:hover': {
                background: 'rgba(25, 118, 210, 0.1)',
                transform: 'scale(1.1)',
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </ModernDialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {dialogLoadingGains && (
            <LoadingBox>
              <CircularProgress sx={{ color: '#1976D2' }} />
            </LoadingBox>
          )}
          
          {dialogGainsError && (
            <StyledAlert severity="error">加载已实现收益数据时出错: {dialogGainsError}</StyledAlert>
          )}
          
          {!dialogLoadingGains && !dialogGainsError && dialogGains.length === 0 && (
            <EmptyStateBox>
              <Typography 
                variant="body1" 
                sx={{
                  color: '#64748B',
                  fontWeight: '500',
                  textAlign: 'center'
                }}
              >
                暂无已实现收益数据
              </Typography>
            </EmptyStateBox>
          )}
          
          {!dialogLoadingGains && !dialogGainsError && dialogGains.length > 0 && (
            <ModernTableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <ModernTableHeadCell>股票代码</ModernTableHeadCell>
                    <ModernTableHeadCell>买入价格</ModernTableHeadCell>
                    <ModernTableHeadCell>买入时间</ModernTableHeadCell>
                    <ModernTableHeadCell>卖出价格</ModernTableHeadCell>
                    <ModernTableHeadCell>卖出时间</ModernTableHeadCell>
                    <ModernTableHeadCell>收益</ModernTableHeadCell>
                    <ModernTableHeadCell>收益率</ModernTableHeadCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {dialogGains.map((trade, index) => {
                    // Calculate cost basis and gain percentage safely
                    const costBasis = trade.buy_quantity * trade.buy_price;
                    const gainPercent = costBasis !== 0 ? (trade.realized_gain / costBasis) : 0;

                    return (
                      <ModernTableRow 
                        key={index}
                        sx={{
                          animation: `slideInRow 0.5s ease-out ${index * 0.05}s both`,
                          '@keyframes slideInRow': {
                            '0%': {
                              opacity: 0,
                              transform: 'translateX(-20px)'
                            },
                            '100%': {
                              opacity: 1,
                              transform: 'translateX(0)'
                            }
                          }
                        }}
                      >
                        <TableCell sx={{ fontWeight: '600', color: '#1976D2' }}>
                          {trade.symbol}
                        </TableCell>
                        <TableCell sx={{ fontWeight: '500' }}>
                          ${trade.buy_price.toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: '500' }}>
                          {new Date(trade.buy_date).toLocaleString()}
                        </TableCell>
                        <TableCell sx={{ fontWeight: '500' }}>
                          ${trade.sell_price.toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: '500' }}>
                          {new Date(trade.sell_date).toLocaleString()}
                        </TableCell>
                        <ValueCell value={trade.realized_gain}>
                          ${trade.realized_gain.toFixed(2)}
                        </ValueCell>
                        <ValueCell value={gainPercent}>
                          {(gainPercent * 100).toFixed(2)}%
                        </ValueCell>
                      </ModernTableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </ModernTableContainer>
          )}
        </DialogContent>
      </ModernDialog>
    </PageCard>
  );
}

export default AccountDetails; 