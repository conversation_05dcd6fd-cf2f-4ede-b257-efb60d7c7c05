import React from 'react';
import {
  Card<PERSON>ontent,
  Typography,
  Box,
  CircularProgress,
  CardHeader,
  Tooltip,
  IconButton,
  useTheme,
  Paper,
  useMediaQuery,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Launch from '@mui/icons-material/Launch';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import RemoveIcon from '@mui/icons-material/Remove';
import {
  GlassCard,
  LoadingBox,
  StyledAlert,
  GradientTypography,
} from '../../styles/common';

// 美化的恐惧贪婪指数卡片
const FearGreedCard = styled(GlassCard)(({ rangeInfo }) => ({
  background: `linear-gradient(135deg, ${rangeInfo?.bgColor || 'rgba(255,255,255,0.9)'} 0%, rgba(255,255,255,0.95) 100%)`,
  border: `1px solid ${rangeInfo?.color || '#1976D2'}20`,
  borderRadius: '16px',
  boxShadow: `0 8px 32px rgba(0,0,0,0.1), 0 0 0 1px ${rangeInfo?.color || '#1976D2'}08`,
  overflow: 'hidden',
  position: 'relative',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 40px rgba(0,0,0,0.15), 0 0 0 1px ${rangeInfo?.color || '#1976D2'}12`,
  },
}));

// 美化的状态纸片
const StatusPaper = styled(Paper)(({ rangeInfo }) => ({
  padding: '16px 24px',
  background: `linear-gradient(135deg, ${rangeInfo?.color || '#1976D2'}15 0%, rgba(255,255,255,0.9) 100%)`,
  border: `2px solid ${rangeInfo?.color || '#1976D2'}25`,
  borderRadius: '12px',
  display: 'flex',
  alignItems: 'center',
  gap: '16px',
  backdropFilter: 'blur(8px)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'scale(1.02)',
    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
  },
}));

// 美化的时间戳纸片
const TimestampPaper = styled(Paper)(({ theme }) => ({
  padding: '8px 16px',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%)',
  backdropFilter: 'blur(10px)',
  borderRadius: '12px',
  border: '1px solid rgba(255,255,255,0.2)',
  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
}));

// 美化的卡片标题
const StyledCardHeader = styled(CardHeader)(({ theme }) => ({
  paddingBottom: '16px',
  paddingTop: '24px',
  paddingLeft: '24px',
  paddingRight: '24px',
  '& .MuiCardHeader-title': {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
}));

const GAUGE_RANGES = [
  { limit: 25, color: '#d32f2f', bgColor: '#ffebee', label: 'Extreme Fear', emoji: '😱', zhLabel: '极度恐惧' },
  { limit: 45, color: '#f57c00', bgColor: '#fff3e0', label: 'Fear', emoji: '😰', zhLabel: '恐惧' },
  { limit: 55, color: '#616161', bgColor: '#f5f5f5', label: 'Neutral', emoji: '😐', zhLabel: '中性' },
  { limit: 75, color: '#388e3c', bgColor: '#e8f5e8', label: 'Greed', emoji: '😊', zhLabel: '贪婪' },
  { limit: 100, color: '#1976d2', bgColor: '#e3f2fd', label: 'Extreme Greed', emoji: '🤑', zhLabel: '极度贪婪' },
];

const getRangeInfo = (value) => {
  if (value === null || value === undefined) {
    const neutralRange = GAUGE_RANGES[2];
    return { ...neutralRange, label: 'N/A', zhLabel: '未知', emoji: '❓' };
  }
  for (const range of GAUGE_RANGES) {
    if (value <= range.limit) {
      return range;
    }
  }
  return GAUGE_RANGES[GAUGE_RANGES.length - 1];
};

const formatTimestamp = (ts) => (ts ? new Date(ts).toLocaleString('zh-CN') : 'N/A');

// Integrated Circular Gauge with Status
const IntegratedGauge = ({ value, size, timestamp }) => {
  const theme = useTheme();
  const rangeInfo = getRangeInfo(value);
  const percentage = value / 100;
  const radius = size * 0.35;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage * circumference);
  const strokeWidth = Math.max(size * 0.06, 12);

  let TrendIcon = RemoveIcon;
  if (value <= 44) {
    TrendIcon = TrendingDownIcon;
  } else if (value >= 56) {
    TrendIcon = TrendingUpIcon;
  }

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center',
      position: 'relative',
      width: '100%',
    }}>
      {/* Circular Gauge */}
      <Box sx={{ position: 'relative', display: 'inline-flex', width: size, height: size }}>
        <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={theme.palette.grey[100]}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle with gradient */}
          <defs>
            <linearGradient id={`gradient-${value}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor={rangeInfo.color} stopOpacity="0.8" />
              <stop offset="100%" stopColor={rangeInfo.color} stopOpacity="1" />
            </linearGradient>
          </defs>
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={`url(#gradient-${value})`}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            style={{
              transition: 'stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1)',
              filter: 'drop-shadow(0 0 12px rgba(0,0,0,0.15))',
            }}
          />
        </svg>
        
        {/* Center Content */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
          }}
        >
          <Typography
            variant="h1"
            component="div"
            sx={{
              fontWeight: 'normal',
              color: rangeInfo.color,
              fontSize: `${size * 0.12}px`,
              lineHeight: 1,
              textShadow: '0 2px 4px rgba(0,0,0,0.1)',
            }}
          >
            {value}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: `${size * 0.035}px`,
              fontWeight: 500,
              mt: 0.5,
            }}
          >
            / 100
          </Typography>
        </Box>
      </Box>

      {/* Integrated Status Display */}
      <Box sx={{ 
        mt: 3, 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center',
        gap: 2
      }}>
        {/* Status with Icon and Text */}
        <StatusPaper rangeInfo={rangeInfo} elevation={3}>
          <TrendIcon 
            sx={{ 
              fontSize: { xs: 32, sm: 40 }, 
              color: rangeInfo.color,
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
            }} 
          />
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="h5"
              fontWeight="normal"
              sx={{
                color: rangeInfo.color,
                lineHeight: 1.2,
                fontSize: { xs: '1.25rem', sm: '1.5rem' }
              }}
            >
              {rangeInfo.zhLabel}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.text.secondary,
                fontWeight: 500,
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}
            >
              {rangeInfo.label}
            </Typography>
          </Box>
          <Typography variant="h4" sx={{ fontSize: '1.5rem' }}>
            {rangeInfo.emoji}
          </Typography>
        </StatusPaper>

        {/* Update Time */}
        <TimestampPaper elevation={1}>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ 
              fontSize: { xs: '0.7rem', sm: '0.75rem' },
              fontWeight: 'normal',
              textAlign: 'center',
              display: 'block',
            }}
          >
            最后更新: {formatTimestamp(timestamp)}
          </Typography>
        </TimestampPaper>
      </Box>
    </Box>
  );
};


const FearAndGreedSection = ({ fearGreedData, fearGreedLoading, fearGreedError, sx }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Dynamic sizing based on screen size
  const gaugeSize = isMobile ? 180 : isTablet ? 220 : 260;
  const isCompactLayout = isMobile || isTablet;

  if (fearGreedLoading) {
    return (
      <GlassCard sx={{ 
        p: { xs: 3, sm: 4, md: 5 },
        minHeight: { xs: 150, sm: 180, md: 200 },
        ...sx 
      }}>
        <LoadingBox>
          <CircularProgress size={isMobile ? 32 : 40} sx={{ color: theme.palette.primary.main }} />
          <Typography variant={isMobile ? "body1" : "h6"} color="text.secondary" sx={{ 
            ml: { xs: 2, sm: 3 }, 
            fontWeight: 500 
          }}>
            正在加载恐惧贪婪指数...
          </Typography>
        </LoadingBox>
      </GlassCard>
    );
  }

  if (fearGreedError) {
    return (
      <StyledAlert severity="error" sx={{ width: '100%', ...sx }}>
        {fearGreedError}
      </StyledAlert>
    );
  }

  if (!fearGreedData || fearGreedData.value === null || fearGreedData.value === undefined) {
    return (
      <StyledAlert severity="warning" sx={{ width: '100%', ...sx }}>
        恐惧贪婪指数数据不可用。
      </StyledAlert>
    );
  }

  const fearAndGreedValue = parseInt(fearGreedData.value, 10);
  const rangeInfo = getRangeInfo(fearAndGreedValue);

  return (
    <FearGreedCard rangeInfo={rangeInfo} sx={sx}>
      {/* Enhanced decorative background */}
      <Box
        sx={{
          position: 'absolute',
          top: -50,
          right: -50,
          width: { xs: 150, sm: 200 },
          height: { xs: 150, sm: 200 },
          background: `radial-gradient(circle, ${rangeInfo.color}08 0%, transparent 70%)`,
          borderRadius: '50%',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -30,
          left: -30,
          width: { xs: 80, sm: 120 },
          height: { xs: 80, sm: 120 },
          background: `radial-gradient(circle, ${rangeInfo.color}05 0%, transparent 70%)`,
          borderRadius: '50%',
        }}
      />
      
      <StyledCardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 1, sm: 1.5 } }}>
            <GradientTypography 
              variant={isMobile ? "h6" : "h5"} 
              sx={{ 
                fontSize: isMobile ? '1.1rem' : '1.3rem',
                fontWeight: 'normal',
              }}
            >
              恐惧贪婪指数
            </GradientTypography>
            <Typography 
              variant={isMobile ? "h6" : "h5"} 
              sx={{ fontSize: { xs: '1.2rem', sm: '1.8rem' } }}
            >
              {rangeInfo.emoji}
            </Typography>
          </Box>
        }
        action={
          <Tooltip title="查看 CNN 恐惧贪婪指数来源" arrow>
            <IconButton
              size={isMobile ? "small" : "medium"}
              onClick={() => window.open('https://edition.cnn.com/markets/fear-and-greed', '_blank')}
              aria-label="View CNN Fear & Greed Index"
              sx={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '12px',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
                  color: 'white',
                  transform: 'scale(1.05)',
                  boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                }
              }}
            >
              <Launch fontSize="small" />
            </IconButton>
          </Tooltip>
        }
      />
      
      <CardContent sx={{ 
        pt: 0, 
        pb: { xs: 3, sm: 4 }, 
        px: { xs: 2, sm: 3 } 
      }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: isCompactLayout ? 'column' : 'row',
            alignItems: 'center',
            gap: { xs: 3, sm: 4, lg: 6 },
            width: '100%',
          }}
        >
          {/* Integrated Gauge Section */}
          <Box sx={{ 
            flex: isCompactLayout ? 'none' : '0 0 auto',
            width: isCompactLayout ? '100%' : 'auto',
          }}>
            <IntegratedGauge value={fearAndGreedValue} size={gaugeSize} timestamp={fearGreedData.timestamp} />
          </Box>

          {/* Analysis Section */}
          <Box sx={{ 
            flex: 1, 
            width: '100%',
            maxWidth: isCompactLayout ? '100%' : 500,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          </Box>
        </Box>
      </CardContent>
    </FearGreedCard>
  );
};

export default FearAndGreedSection; 