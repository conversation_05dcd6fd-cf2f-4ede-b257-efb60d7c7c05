import React, { useState } from 'react';
import { Grid, Box } from '@mui/material';
import CompositeChart from '../components/charts/CompositeChart';
import AllAccountSummary from '../components/common/AllAccountSummary';
import AccountDetails from '../components/common/AccountDetails';
import NavBar from '../components/layout/NavBar';
import StockPerformance from '../components/common/StockPerformance';

function HomePage() {
  const [refreshCounter, setRefreshCounter] = useState(0);

  // 刷新函数暴露给子组件
  const triggerRefresh = () => {
    setRefreshCounter(prev => prev + 1);
  };

  return (
    <>
      <NavBar />
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <CompositeChart key={`composite-${refreshCounter}`} />
          </Grid>
          <Grid item xs={12}>
            <AllAccountSummary onRefresh={triggerRefresh} />
          </Grid>
          <Grid item xs={12} md={6}>
            <AccountDetails refreshKey={refreshCounter} />
          </Grid>
          <Grid item xs={12} md={6}>
            <StockPerformance refreshKey={refreshCounter} />
          </Grid>
        </Grid>
      </Box>
    </>
  );
}

export default HomePage; 