import { styled } from '@mui/material/styles';
import {
  Card,
  Box,
  Button,
  Dialog,
  DialogTitle,
  TableContainer,
  TableCell,
  TableRow,
  Alert,
  Typography,
} from '@mui/material';

// --- Containers ---

export const PageCard = styled(Card)(({ theme }) => ({
  height: '100%',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%)',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
  border: '1px solid rgba(255,255,255,0.2)',
  overflow: 'hidden',
}));

export const GlassCard = styled(Box)(({ theme }) => ({
  marginBottom: '16px',
  padding: '20px',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%)',
  backdropFilter: 'blur(8px)',
  border: '1px solid rgba(255,255,255,0.2)',
  borderRadius: '12px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 16px rgba(0,0,0,0.12)',
    background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,1) 100%)',
  },
}));

export const CollapseContent = styled(Box)(({ theme }) => ({
  padding: '16px',
  borderRadius: '12px',
  background: 'linear-gradient(135deg, rgba(248,250,252,0.6) 0%, rgba(255,255,255,0.8) 100%)',
  backdropFilter: 'blur(8px)',
  boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.06)',
  border: '1px solid rgba(255,255,255,0.3)',
}));

// --- Typography ---

export const GradientTypography = styled(Typography)(({ theme }) => ({
  fontWeight: '700',
  background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  fontSize: '1.3rem',
}));


// --- Buttons ---

export const PrimaryButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
  color: 'white',
  borderRadius: '12px',
  padding: '10px 20px',
  fontSize: '0.9rem',
  fontWeight: '600',
  textTransform: 'none',
  boxShadow: '0 4px 16px rgba(25, 118, 210, 0.3)',
  border: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    background: 'linear-gradient(135deg, #1565C0 0%, #0D47A1 100%)',
    boxShadow: '0 6px 20px rgba(25, 118, 210, 0.4)',
    transform: 'translateY(-2px)',
  },
}));

export const SecondaryButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(21, 101, 192, 0.05) 100%)',
  color: '#1976D2',
  border: '1px solid rgba(25, 118, 210, 0.2)',
  borderRadius: '12px',
  padding: '8px 16px',
  fontSize: '0.85rem',
  fontWeight: '600',
  textTransform: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.15) 0%, rgba(21, 101, 192, 0.08) 100%)',
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
  },
}));

export const DestructiveButton = styled(Button)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(198, 40, 40, 0.05) 100%)',
  color: '#C62828',
  border: '1px solid rgba(244, 67, 54, 0.2)',
  borderRadius: '12px',
  padding: '8px 16px',
  fontSize: '0.85rem',
  fontWeight: '600',
  textTransform: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(198, 40, 40, 0.08) 100%)',
    transform: 'translateY(-1px)',
    boxShadow: '0 4px 12px rgba(244, 67, 54, 0.2)',
  },
}));


// --- Dialogs ---

export const ModernDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '16px',
    background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%)',
    backdropFilter: 'blur(20px)',
    boxShadow: '0 24px 48px rgba(0,0,0,0.15)',
    border: '1px solid rgba(255,255,255,0.2)',
  },
}));

export const ModernDialogTitle = styled(DialogTitle)(({ theme }) => ({
  background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  fontWeight: '700',
  fontSize: '1.3rem',
  padding: '24px 24px 16px',
  position: 'relative',
}));

// --- Tables ---

export const ModernTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  overflow: 'hidden',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%)',
  backdropFilter: 'blur(10px)',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.2)',
}));

export const ModernTableHeadCell = styled(TableCell)(({ theme }) => ({
  fontWeight: '700',
  padding: '16px 20px',
  background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%)',
  color: '#1976D2',
  borderBottom: '2px solid rgba(25, 118, 210, 0.1)',
  fontSize: '0.95rem',
}));

export const ModernTableRow = styled(TableRow)(({ theme }) => ({
  transition: 'all 0.2s ease',
  '&:hover': {
    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.04) 0%, rgba(21, 101, 192, 0.02) 100%)',
  },
  '&:nth-of-type(even)': {
    background: 'rgba(248, 250, 252, 0.3)',
  },
}));

export const ValueCell = styled(TableCell)(({ value }) => ({
  color: value > 0 ? '#2E7D32' : value < 0 ? '#C62828' : '#64748B',
  fontWeight: '600',
  padding: '14px 20px',
  fontSize: '0.9rem',
  background: value > 0 
    ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(46, 125, 50, 0.05) 100%)'
    : value < 0 
    ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(198, 40, 40, 0.05) 100%)'
    : 'transparent',
  borderRadius: '4px',
  transition: 'all 0.2s ease',
}));

// --- Status Displays ---

export const LoadingBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '200px',
  borderRadius: '12px',
  background: 'linear-gradient(135deg, rgba(248,250,252,0.6) 0%, rgba(255,255,255,0.8) 100%)',
  backdropFilter: 'blur(8px)',
}));

export const StyledAlert = styled(Alert)(({ theme }) => ({
  borderRadius: '12px',
  background: 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(198, 40, 40, 0.05) 100%)',
  backdropFilter: 'blur(8px)',
  border: '1px solid rgba(244, 67, 54, 0.2)',
}));

export const EmptyStateBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '200px',
  borderRadius: '12px',
  background: 'rgba(248, 250, 252, 0.5)',
  border: '2px dashed rgba(148, 163, 184, 0.3)',
})); 

// --- Chart Containers ---

export const ChartContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '500px',
  padding: '16px',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%)',
  backdropFilter: 'blur(10px)',
  borderRadius: '12px',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.2)',
}));