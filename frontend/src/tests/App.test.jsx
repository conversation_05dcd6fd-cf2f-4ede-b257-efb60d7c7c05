import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import App from '../App'
import { createMockFetch } from './mocks/apiMocks'
import { testAccounts } from './fixtures/testData'

// Mock the page components to avoid complex dependencies
vi.mock('../pages/HomePage', () => {
  const NavBar = vi.fn(() => <nav role="navigation" data-testid="navbar">Navigation</nav>)
  return {
    default: () => (
      <>
        <NavBar />
        <div data-testid="home-page">Home Page</div>
      </>
    )
  }
})

vi.mock('../pages/AccountsPage', () => ({
  default: () => <div data-testid="accounts-page">Accounts Page</div>
}))

vi.mock('../pages/StrategiesPage', () => ({
  default: () => <div data-testid="strategies-page">Strategies Page</div>
}))

vi.mock('../pages/ReferencePage', () => ({
  default: () => <div data-testid="reference-page">Reference Page</div>
}))

// Mock the NavBar component to avoid Router dependencies
vi.mock('../components/layout/NavBar', () => ({
  default: () => <nav role="navigation" data-testid="navbar">Navigation</nav>
}))

// Note: react-router-dom is mocked globally in setup.js

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders without crashing', () => {
    render(<App />)
    expect(screen.getByTestId('home-page')).toBeInTheDocument()
  })

  it('applies the correct theme configuration', () => {
    const { container } = render(<App />)

    // Check if CssBaseline is applied (should reset margins)
    const body = document.body
    expect(body).toHaveStyle({ margin: '0' })
  })

  it('renders the Toaster component', () => {
    render(<App />)

    // The Toaster component should be in the DOM (mocked)
    // We can't directly test the toast functionality without triggering it
    expect(document.body).toBeInTheDocument()
  })

  it('has correct background styling', () => {
    const { container } = render(<App />)

    // Find the Box component with background styling
    const backgroundBox = container.querySelector('[class*="MuiBox-root"]')
    expect(backgroundBox).toBeInTheDocument()
  })

  describe('Routing', () => {
    it('renders HomePage by default', () => {
      render(<App />)
      expect(screen.getByTestId('home-page')).toBeInTheDocument()
    })

    it('renders navigation links', () => {
      render(<App />)

      // Check that navigation is present
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })

    it('includes all required route components', () => {
      render(<App />)

      // The app should render without errors, indicating all routes are properly configured
      expect(screen.getByTestId('home-page')).toBeInTheDocument()
    })
  })

  describe('Theme Configuration', () => {
    it('has correct primary color configuration', () => {
      const { container } = render(<App />)

      // The theme should be applied to the ThemeProvider
      // We can test this by checking if components receive the theme
      expect(container).toBeInTheDocument()
    })

    it('has correct typography configuration', () => {
      const { container } = render(<App />)

      // Typography should be applied through the theme
      expect(container).toBeInTheDocument()
    })

    it('has correct component overrides', () => {
      const { container } = render(<App />)

      // Component overrides should be applied through the theme
      expect(container).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('handles component errors gracefully', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // This test would require an error boundary implementation
      // For now, we just ensure the app doesn't crash
      render(<App />)
      expect(screen.getByTestId('home-page')).toBeInTheDocument()

      consoleSpy.mockRestore()
    })
  })

  describe('Accessibility', () => {
    it('has proper document structure', () => {
      render(<App />)

      // Should have proper semantic structure
      expect(document.body).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Tab navigation should work
      await user.tab()
      expect(document.activeElement).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('renders efficiently', () => {
      const startTime = performance.now()
      render(<App />)
      const endTime = performance.now()

      // Should render within reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100)
    })
  })

  describe('Integration', () => {
    it('integrates with all providers correctly', () => {
      render(<App />)

      // Should render without errors when all providers are present
      expect(screen.getByTestId('home-page')).toBeInTheDocument()
    })

    it('handles provider context correctly', () => {
      render(<App />)

      // RefreshProvider should be available to child components
      expect(screen.getByTestId('home-page')).toBeInTheDocument()
    })
  })
})
