import { describe, it, expect, vi } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useContext } from 'react'
import { RefreshProvider, RefreshContext } from '../../contexts/RefreshContext'

// Test component to consume the context
const TestConsumer = () => {
  const { refreshFlag, setRefreshFlag } = useContext(RefreshContext)
  
  return (
    <div>
      <div data-testid="refresh-flag">{refreshFlag.toString()}</div>
      <button 
        data-testid="toggle-refresh" 
        onClick={() => setRefreshFlag(!refreshFlag)}
      >
        Toggle Refresh
      </button>
      <button 
        data-testid="set-true" 
        onClick={() => setRefreshFlag(true)}
      >
        Set True
      </button>
      <button 
        data-testid="set-false" 
        onClick={() => setRefreshFlag(false)}
      >
        Set False
      </button>
    </div>
  )
}

// Test component that uses context without provider
const TestConsumerWithoutProvider = () => {
  const context = useContext(RefreshContext)
  
  return (
    <div>
      <div data-testid="context-value">
        {context ? 'Context Available' : 'No Context'}
      </div>
      <div data-testid="refresh-flag">
        {context?.refreshFlag?.toString() || 'undefined'}
      </div>
    </div>
  )
}

describe('RefreshContext', () => {
  describe('RefreshProvider', () => {
    it('provides initial context values', () => {
      render(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )
      
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('false')
    })

    it('allows updating refresh flag state', async () => {
      const user = userEvent.setup()
      
      render(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )
      
      // Initial state should be false
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('false')
      
      // Toggle to true
      await user.click(screen.getByTestId('toggle-refresh'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('true')
      
      // Toggle back to false
      await user.click(screen.getByTestId('toggle-refresh'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('false')
    })

    it('allows setting refresh flag to true', async () => {
      const user = userEvent.setup()
      
      render(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )
      
      await user.click(screen.getByTestId('set-true'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('true')
    })

    it('allows setting refresh flag to false', async () => {
      const user = userEvent.setup()
      
      render(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )
      
      // First set to true
      await user.click(screen.getByTestId('set-true'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('true')
      
      // Then set to false
      await user.click(screen.getByTestId('set-false'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('false')
    })

    it('maintains state across re-renders', async () => {
      const user = userEvent.setup()

      const { rerender } = render(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )

      // Change state
      await user.click(screen.getByTestId('set-true'))
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('true')

      // Re-render the provider - state should persist
      rerender(
        <RefreshProvider>
          <TestConsumer />
        </RefreshProvider>
      )

      // State should persist across re-renders (React context behavior)
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('true')
    })

    it('supports multiple consumers', async () => {
      const user = userEvent.setup()
      
      render(
        <RefreshProvider>
          <TestConsumer />
          <TestConsumer />
        </RefreshProvider>
      )
      
      const refreshFlags = screen.getAllByTestId('refresh-flag')
      const toggleButtons = screen.getAllByTestId('toggle-refresh')
      
      // Both consumers should have the same initial state
      expect(refreshFlags[0]).toHaveTextContent('false')
      expect(refreshFlags[1]).toHaveTextContent('false')
      
      // Clicking one button should update both consumers
      await user.click(toggleButtons[0])
      
      expect(refreshFlags[0]).toHaveTextContent('true')
      expect(refreshFlags[1]).toHaveTextContent('true')
    })

    it('renders children correctly', () => {
      render(
        <RefreshProvider>
          <div data-testid="child">Child Component</div>
        </RefreshProvider>
      )
      
      expect(screen.getByTestId('child')).toHaveTextContent('Child Component')
    })
  })

  describe('RefreshContext without Provider', () => {
    it('provides default context values when used without provider', () => {
      render(<TestConsumerWithoutProvider />)
      
      expect(screen.getByTestId('context-value')).toHaveTextContent('Context Available')
      expect(screen.getByTestId('refresh-flag')).toHaveTextContent('false')
    })
  })

  describe('Context API Integration', () => {
    it('works with useContext hook', () => {
      const TestComponent = () => {
        const context = useContext(RefreshContext)
        return (
          <div data-testid="context-type">
            {typeof context.setRefreshFlag}
          </div>
        )
      }
      
      render(
        <RefreshProvider>
          <TestComponent />
        </RefreshProvider>
      )
      
      expect(screen.getByTestId('context-type')).toHaveTextContent('function')
    })

    it('provides stable function references', () => {
      let setRefreshFlagRef1, setRefreshFlagRef2
      
      const TestComponent = ({ onRender }) => {
        const { setRefreshFlag } = useContext(RefreshContext)
        onRender(setRefreshFlag)
        return <div>Test</div>
      }
      
      const { rerender } = render(
        <RefreshProvider>
          <TestComponent onRender={(fn) => { setRefreshFlagRef1 = fn }} />
        </RefreshProvider>
      )
      
      rerender(
        <RefreshProvider>
          <TestComponent onRender={(fn) => { setRefreshFlagRef2 = fn }} />
        </RefreshProvider>
      )
      
      // Function references should be different for different provider instances
      // but stable within the same provider instance
      expect(typeof setRefreshFlagRef1).toBe('function')
      expect(typeof setRefreshFlagRef2).toBe('function')
    })
  })

  describe('Error Handling', () => {
    it('handles invalid context usage gracefully', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      render(<TestConsumerWithoutProvider />)
      
      // Should not crash even when used without provider
      expect(screen.getByTestId('context-value')).toBeInTheDocument()
      
      consoleSpy.mockRestore()
    })
  })

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', async () => {
      const user = userEvent.setup()
      let renderCount = 0
      
      const TestComponent = () => {
        renderCount++
        const { refreshFlag, setRefreshFlag } = useContext(RefreshContext)
        
        return (
          <div>
            <div data-testid="render-count">{renderCount}</div>
            <div data-testid="refresh-flag">{refreshFlag.toString()}</div>
            <button onClick={() => setRefreshFlag(!refreshFlag)}>
              Toggle
            </button>
          </div>
        )
      }
      
      render(
        <RefreshProvider>
          <TestComponent />
        </RefreshProvider>
      )
      
      // Initial render
      expect(screen.getByTestId('render-count')).toHaveTextContent('1')
      
      // State change should cause re-render
      await user.click(screen.getByRole('button'))
      expect(screen.getByTestId('render-count')).toHaveTextContent('2')
      
      // Same state should not cause re-render
      await user.click(screen.getByRole('button'))
      expect(screen.getByTestId('render-count')).toHaveTextContent('3')
    })
  })
})
