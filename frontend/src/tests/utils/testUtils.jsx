import React from 'react'
import { render } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { RefreshProvider } from '../../contexts/RefreshContext'

// Create test theme matching the app theme
const testTheme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#388e3c',
      light: '#4caf50',
      dark: '#2e7d32',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
})

// Custom render function that includes all providers
export function renderWithProviders(ui, options = {}) {
  const {
    initialEntries = ['/'],
    theme = testTheme,
    ...renderOptions
  } = options

  function Wrapper({ children }) {
    return (
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <RefreshProvider>
            {children}
          </RefreshProvider>
        </ThemeProvider>
      </BrowserRouter>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Custom render for components that don't need routing
export function renderWithTheme(ui, options = {}) {
  const { theme = testTheme, ...renderOptions } = options

  function Wrapper({ children }) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <RefreshProvider>
          {children}
        </RefreshProvider>
      </ThemeProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock data generators
export const mockAccountData = {
  account_breakdown: [
    {
      account_id: 1,
      name: 'Test Account 1',
      current_value: 10000,
      total_cost: 8000,
      total_gain: 2000,
      gain_rate: 25.0,
      unrealized_gain: 1500,
      realized_gain: 500,
    },
    {
      account_id: 2,
      name: 'Test Account 2',
      current_value: 5000,
      total_cost: 5500,
      total_gain: -500,
      gain_rate: -9.09,
      unrealized_gain: -300,
      realized_gain: -200,
    },
  ]
}

export const mockHoldingsData = {
  holdings: [
    {
      symbol: 'AAPL',
      account_name: 'Test Account 1',
      quantity: 10,
      avg_price: 150.0,
      current_price: 175.0,
      total_cost: 1500.0,
      current_value: 1750.0,
      gain: 250.0,
      return_rate: 16.67,
    },
    {
      symbol: 'GOOGL',
      account_name: 'Test Account 1',
      quantity: 5,
      avg_price: 2500.0,
      current_price: 2400.0,
      total_cost: 12500.0,
      current_value: 12000.0,
      gain: -500.0,
      return_rate: -4.0,
    },
  ]
}

export const mockStrategiesData = {
  positions: [
    {
      symbol: 'AAPL',
      current_price: 175.0,
      avg_price: 150.0,
      quantity: 10,
      rsi: 65.5,
      recommendation: 'HOLD',
    },
    {
      symbol: 'GOOGL',
      current_price: 2400.0,
      avg_price: 2500.0,
      quantity: 5,
      rsi: 45.2,
      recommendation: 'BUY',
    },
  ]
}

// API response helpers
export const createMockResponse = (data, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  statusText: status === 200 ? 'OK' : 'Error',
  headers: new Map([['content-type', 'application/json']]),
  json: () => Promise.resolve(data),
})

export const createMockErrorResponse = (message = 'API Error', status = 500) => ({
  ok: false,
  status,
  statusText: 'Error',
  headers: new Map([['content-type', 'application/json']]),
  json: () => Promise.resolve({ error: message }),
})

// Test helpers for async operations
export const waitForLoadingToFinish = async () => {
  const { waitForElementToBeRemoved } = await import('@testing-library/react')
  try {
    await waitForElementToBeRemoved(() => document.querySelector('[data-testid="loading"]'), {
      timeout: 3000
    })
  } catch (error) {
    // Loading element might not exist, which is fine
  }
}

// Custom matchers for better assertions
export const customMatchers = {
  toHaveLoadingState: (received) => {
    const hasLoading = received.querySelector('[data-testid="loading"]') !== null ||
                     received.querySelector('.MuiCircularProgress-root') !== null
    
    return {
      message: () => `Expected element ${hasLoading ? 'not ' : ''}to have loading state`,
      pass: hasLoading,
    }
  },
  
  toHaveErrorState: (received, expectedMessage) => {
    const errorElement = received.querySelector('[data-testid="error"]') ||
                        received.querySelector('.MuiAlert-standardError')
    
    if (!errorElement) {
      return {
        message: () => 'Expected element to have error state',
        pass: false,
      }
    }
    
    if (expectedMessage && !errorElement.textContent.includes(expectedMessage)) {
      return {
        message: () => `Expected error message to contain "${expectedMessage}", but got "${errorElement.textContent}"`,
        pass: false,
      }
    }
    
    return {
      message: () => 'Expected element not to have error state',
      pass: true,
    }
  },
}

// Re-export everything from testing-library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
