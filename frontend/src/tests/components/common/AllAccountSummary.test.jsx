import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AllAccountSummary from '../../../components/common/AllAccountSummary'
import { renderWithProviders } from '../../utils/testUtils'
import { createMockFetch, createMockFetchWithErrors } from '../../mocks/apiMocks'
import { testAccounts, testHoldings } from '../../fixtures/testData'

// Mock the form dialogs
vi.mock('../../../components/forms/AddAccountDialog', () => ({
  default: ({ open, onClose, onSubmit }) => (
    open ? (
      <div data-testid="add-account-dialog">
        <button onClick={async () => {
          try {
            await onSubmit({ name: 'New Account' });
          } catch (error) {
            // Handle the error to prevent unhandled promise rejection
            console.log('Account creation failed:', error.message);
          }
        }}>
          Submit
        </button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null
  )
}))

vi.mock('../../../components/forms/ImportTransactionsDialog', () => ({
  default: ({ open, onClose, onSuccess, accountId }) => (
    open ? (
      <div data-testid="import-dialog">
        <div data-testid="selected-account">{accountId}</div>
        <button onClick={() => onSuccess(5)}>Import Success</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null
  )
}))

describe('AllAccountSummary Component', () => {
  const mockOnRefresh = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset toast mocks
    global.mockToast.success.mockClear()
    global.mockToast.error.mockClear()

    global.fetch = createMockFetch({
      overview: { account_breakdown: testAccounts }
    })
  })

  describe('Initial Rendering', () => {
    it('renders loading state initially', () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('renders account data after loading', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
        expect(screen.getByText('Test Account 2')).toBeInTheDocument()
      })
    })

    it('displays correct account information', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)

      await waitFor(() => {
        expect(screen.getByText('$10,000')).toBeInTheDocument()
        expect(screen.getByText('$5,000')).toBeInTheDocument()
        expect(screen.getByText('+25.00%')).toBeInTheDocument()
        expect(screen.getByText('-9.09%')).toBeInTheDocument()
      })
    })

    it('renders table headers correctly', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)

      await waitFor(() => {
        expect(screen.getByText('账户名称')).toBeInTheDocument()
        expect(screen.getByText('当前市值 (USD)')).toBeInTheDocument()
        expect(screen.getByText('已实现收益 (USD)')).toBeInTheDocument()
        expect(screen.getByText('总收益 (USD)')).toBeInTheDocument()
        expect(screen.getByText('持仓收益率')).toBeInTheDocument()
      })
    })
  })

  describe('Account Actions', () => {
    it('opens add account dialog when button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('新增账户'))
      expect(screen.getByTestId('add-account-dialog')).toBeInTheDocument()
    })

    it('handles account creation successfully', async () => {
      const user = userEvent.setup()
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 201,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ id: 3, name: 'New Account' })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: [...testAccounts, { id: 3, name: 'New Account' }] })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('新增账户'))
      await user.click(screen.getByText('Submit'))

      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith('账户创建成功')
      })
    })

    it('handles account deletion', async () => {
      const user = userEvent.setup()
      // Mock window.confirm
      window.confirm = vi.fn(() => true)
      
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ success: true })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: [testAccounts[1]] })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const deleteButtons = screen.getAllByTestId('DeleteIcon')
      await user.click(deleteButtons[0])
      
      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith('账户删除成功')
      })
    })

    it('cancels account deletion when user declines', async () => {
      const user = userEvent.setup()
      window.confirm = vi.fn(() => false)
      
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const deleteButtons = screen.getAllByTestId('DeleteIcon')
      await user.click(deleteButtons[0])
      
      expect(window.confirm).toHaveBeenCalled()
      // Should not make delete API call
      expect(global.fetch).toHaveBeenCalledTimes(1) // Only initial load
    })
  })

  describe('Holdings Details', () => {
    it('expands account details when clicked', async () => {
      const user = userEvent.setup()
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ holdings: testHoldings.filter(h => h.account_id === 1) })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0]
      await user.click(expandButton)
      
      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument()
        expect(screen.getByText('AAPL')).toBeInTheDocument()
        expect(screen.getByText('GOOGL')).toBeInTheDocument()
      })
    })

    it('shows loading state while fetching holdings', async () => {
      const user = userEvent.setup()
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockImplementationOnce(() => new Promise(() => {})) // Never resolves

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0]
      await user.click(expandButton)
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('collapses holdings when clicked again', async () => {
      const user = userEvent.setup()
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ holdings: testHoldings.filter(h => h.account_id === 1) })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const expandButton = screen.getAllByTestId('KeyboardArrowDownIcon')[0]
      await user.click(expandButton)
      
      await waitFor(() => {
        expect(screen.getByText('持仓明细')).toBeInTheDocument()
      })
      
      const collapseButton = screen.getByTestId('KeyboardArrowUpIcon')
      await user.click(collapseButton)
      
      expect(screen.queryByText('持仓明细')).not.toBeInTheDocument()
    })
  })

  describe('Import Transactions', () => {
    it('opens import dialog when import button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const importButtons = screen.getAllByTestId('UploadIcon')
      await user.click(importButtons[0])
      
      expect(screen.getByTestId('import-dialog')).toBeInTheDocument()
      expect(screen.getByTestId('selected-account')).toHaveTextContent('1')
    })

    it('handles successful import', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const importButtons = screen.getAllByTestId('UploadIcon')
      await user.click(importButtons[0])
      
      await user.click(screen.getByText('Import Success'))
      
      await waitFor(() => {
        expect(global.mockToast.success).toHaveBeenCalledWith('导入完成: 共导入 5 条记录')
        expect(mockOnRefresh).toHaveBeenCalled()
      })
    })
  })

  describe('Error Handling', () => {
    it('displays error message when API fails', async () => {
      global.fetch = createMockFetchWithErrors({
        '/analytics/overview': { status: 500, message: 'Server error' }
      })
      
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('获取账户数据失败')).toBeInTheDocument()
      })
    })

    it('handles account creation error', async () => {
      const user = userEvent.setup()
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Account name already exists' })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument()
      })
      
      await user.click(screen.getByText('新增账户'))
      await user.click(screen.getByText('Submit'))
      
      await waitFor(() => {
        expect(global.mockToast.error).toHaveBeenCalledWith('创建账户失败: Account name already exists')
      })
    })

    it('handles account deletion error', async () => {
      const user = userEvent.setup()
      window.confirm = vi.fn(() => true)
      
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ account_breakdown: testAccounts })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Cannot delete account with holdings' })
        })

      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Account 1')).toBeInTheDocument()
      })
      
      const deleteButtons = screen.getAllByTestId('DeleteIcon')
      await user.click(deleteButtons[0])
      
      await waitFor(() => {
        expect(global.mockToast.error).toHaveBeenCalledWith('删除账户失败: Cannot delete account with holdings')
      })
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument()
        expect(screen.getByRole('columnheader', { name: /账户名称/ })).toBeInTheDocument()
      })
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AllAccountSummary onRefresh={mockOnRefresh} />)
      
      await waitFor(() => {
        expect(screen.getByText('新增账户')).toBeInTheDocument()
      })
      
      await user.tab()
      expect(document.activeElement).toBeInTheDocument()
    })
  })
})
