import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import OptionsAnalysisPanel from '../../../components/options/OptionsAnalysisPanel'
import { createMockFetch } from '../../mocks/apiMocks'

// Mock the child components
vi.mock('../../../components/options/WatchlistDialog', () => ({
  default: ({ open, onClose, onWatchlistsUpdated }) => (
    open ? (
      <div data-testid="watchlist-dialog">
        <button onClick={onClose}>Close</button>
        <button onClick={() => onWatchlistsUpdated()}>Update Watchlists</button>
      </div>
    ) : null
  )
}))

vi.mock('../../../components/options/StrategyConfigDialog', () => ({
  default: ({ open, onClose, onConfigSaved, strategyType }) => (
    open ? (
      <div data-testid="strategy-config-dialog">
        <span>Config for {strategyType}</span>
        <button onClick={onClose}>Close</button>
        <button onClick={() => onConfigSaved({ min_dte: 30 })}>Save Config</button>
      </div>
    ) : null
  )
}))

describe('OptionsAnalysisPanel', () => {
  const mockAccount = { account_id: 1, name: 'Test Account' }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders without crashing', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()
  })

  it('displays strategy selection tabs', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    expect(screen.getByText('现金担保看跌期权')).toBeInTheDocument()
    expect(screen.getByText('备兑看涨期权')).toBeInTheDocument()
    expect(screen.getByText('铁鹰策略')).toBeInTheDocument()
  })

  it('allows strategy selection', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const putTab = screen.getByText('现金担保看跌期权')
    await user.click(putTab)

    // Should update the selected strategy
    expect(putTab.closest('[role="tab"]')).toHaveAttribute('aria-selected', 'true')
  })

  it('loads watchlists on mount', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] },
      { watchlist_id: 2, name: 'Finance', symbols: ['JPM', 'BAC'] }
    ]

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
    })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists'),
        expect.objectContaining({
          method: 'GET'
        })
      )
    })
  })

  it('opens watchlist dialog when manage button is clicked', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const manageButton = screen.getByText('管理观察列表')
    await user.click(manageButton)

    expect(screen.getByTestId('watchlist-dialog')).toBeInTheDocument()
  })

  it('opens strategy config dialog when settings button is clicked', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const settingsButton = screen.getByLabelText('策略配置')
    await user.click(settingsButton)

    expect(screen.getByTestId('strategy-config-dialog')).toBeInTheDocument()
  })

  it('disables analysis button when no watchlist is selected', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).toBeDisabled()
  })

  it('enables analysis button when watchlist is selected', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] }
    ]

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
    })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    await waitFor(() => {
      const watchlistSelect = screen.getByDisplayValue('')
      expect(watchlistSelect).toBeInTheDocument()
    })

    // Select a watchlist
    const watchlistSelect = screen.getByDisplayValue('')
    await user.click(watchlistSelect)

    const option = screen.getByText('Tech Stocks')
    await user.click(option)

    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).not.toBeDisabled()
  })

  it('performs analysis when analyze button is clicked', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] }
    ]

    const mockAnalysisResults = {
      candidates: [
        {
          symbol: 'AAPL',
          strike: 145,
          expiration: '2024-02-16',
          premium: 300,
          annualizedRoi: 0.15,
          winRateScore: 75
        }
      ],
      market_conditions: {
        volatility_regime: 'Normal',
        stress_indicator: 45
      },
      status: 'success'
    }

    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAnalysisResults)
      })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for watchlists to load and select one
    await waitFor(() => {
      expect(screen.getByDisplayValue('')).toBeInTheDocument()
    })

    const watchlistSelect = screen.getByDisplayValue('')
    await user.click(watchlistSelect)

    const option = screen.getByText('Tech Stocks')
    await user.click(option)

    // Click analyze button
    const analyzeButton = screen.getByText('开始分析')
    await user.click(analyzeButton)

    // Should show loading state
    expect(screen.getByText('分析中...')).toBeInTheDocument()

    // Wait for analysis to complete
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/analyze'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.stringContaining('cash_secured_puts')
        })
      )
    })
  })

  it('displays analysis results after successful analysis', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL'] }
    ]

    const mockAnalysisResults = {
      candidates: [
        {
          symbol: 'AAPL',
          strike: 145,
          expiration: '2024-02-16',
          premium: 300,
          annualizedRoi: 0.15,
          winRateScore: 75
        }
      ],
      market_conditions: {
        volatility_regime: 'Normal',
        stress_indicator: 45
      },
      status: 'success'
    }

    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAnalysisResults)
      })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Select watchlist and analyze
    await waitFor(() => {
      expect(screen.getByDisplayValue('')).toBeInTheDocument()
    })

    const watchlistSelect = screen.getByDisplayValue('')
    await user.click(watchlistSelect)
    await user.click(screen.getByText('Tech Stocks'))

    const analyzeButton = screen.getByText('开始分析')
    await user.click(analyzeButton)

    // Wait for results
    await waitFor(() => {
      expect(screen.getByText('分析结果')).toBeInTheDocument()
    })

    // Should display market conditions
    expect(screen.getByText('市场状况')).toBeInTheDocument()
    expect(screen.getByText('Normal')).toBeInTheDocument()

    // Should display candidates
    expect(screen.getByText('AAPL')).toBeInTheDocument()
    expect(screen.getByText('145')).toBeInTheDocument()
  })

  it('handles analysis errors gracefully', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL'] }
    ]

    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Analysis failed' })
      })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Select watchlist and analyze
    await waitFor(() => {
      expect(screen.getByDisplayValue('')).toBeInTheDocument()
    })

    const watchlistSelect = screen.getByDisplayValue('')
    await user.click(watchlistSelect)
    await user.click(screen.getByText('Tech Stocks'))

    const analyzeButton = screen.getByText('开始分析')
    await user.click(analyzeButton)

    // Should handle error and stop loading
    await waitFor(() => {
      expect(screen.queryByText('分析中...')).not.toBeInTheDocument()
    })
  })

  it('updates config when strategy config dialog saves', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Open config dialog
    const settingsButton = screen.getByLabelText('策略配置')
    await user.click(settingsButton)

    // Save config
    const saveButton = screen.getByText('Save Config')
    await user.click(saveButton)

    // Dialog should close
    expect(screen.queryByTestId('strategy-config-dialog')).not.toBeInTheDocument()
  })

  it('refreshes watchlists when watchlist dialog updates', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: [], status: 'success' })
    })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Open watchlist dialog
    const manageButton = screen.getByText('管理观察列表')
    await user.click(manageButton)

    // Trigger update
    const updateButton = screen.getByText('Update Watchlists')
    await user.click(updateButton)

    // Should call fetch again to reload watchlists
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2) // Initial load + refresh
    })
  })
})
