import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import WatchlistDialog from '../../../components/options/WatchlistDialog'
import { createMockFetch } from '../../mocks/apiMocks'

describe('WatchlistDialog', () => {
  const mockProps = {
    open: true,
    onClose: vi.fn(),
    selectedAccount: 1,
    onWatchlistsUpdated: vi.fn()
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders when open', () => {
    render(<WatchlistDialog {...mockProps} />)
    
    expect(screen.getByText('管理观察列表')).toBeInTheDocument()
    expect(screen.getByText('创建新观察列表')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<WatchlistDialog {...mockProps} open={false} />)
    
    expect(screen.queryByText('管理观察列表')).not.toBeInTheDocument()
  })

  it('loads existing watchlists on open', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] },
      { watchlist_id: 2, name: 'Finance', symbols: ['JPM', 'BAC'] }
    ]
    
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
    })
    
    render(<WatchlistDialog {...mockProps} />)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists'),
        expect.objectContaining({
          method: 'GET'
        })
      )
    })
    
    expect(screen.getByText('Tech Stocks')).toBeInTheDocument()
    expect(screen.getByText('Finance')).toBeInTheDocument()
  })

  it('allows creating a new watchlist', async () => {
    const user = userEvent.setup()
    
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: [], status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlist_id: 1, status: 'success' })
      })
    
    render(<WatchlistDialog {...mockProps} />)
    
    // Fill in the form
    const nameInput = screen.getByLabelText('观察列表名称')
    await user.type(nameInput, 'New Watchlist')
    
    const symbolsInput = screen.getByLabelText('股票代码')
    await user.type(symbolsInput, 'AAPL,MSFT,GOOGL')
    
    // Submit the form
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.stringContaining('New Watchlist')
        })
      )
    })
    
    expect(mockProps.onWatchlistsUpdated).toHaveBeenCalled()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    // Try to save without filling required fields
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    // Should show validation errors
    expect(screen.getByText('请输入观察列表名称')).toBeInTheDocument()
    expect(screen.getByText('请输入至少一个股票代码')).toBeInTheDocument()
  })

  it('validates symbol format', async () => {
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    const nameInput = screen.getByLabelText('观察列表名称')
    await user.type(nameInput, 'Test List')
    
    const symbolsInput = screen.getByLabelText('股票代码')
    await user.type(symbolsInput, 'invalid-symbol,123')
    
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    // Should show symbol validation error
    expect(screen.getByText('股票代码格式不正确')).toBeInTheDocument()
  })

  it('allows editing existing watchlists', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] }
    ]
    
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlist_id: 1, status: 'success' })
      })
    
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    // Wait for watchlists to load
    await waitFor(() => {
      expect(screen.getByText('Tech Stocks')).toBeInTheDocument()
    })
    
    // Click edit button
    const editButton = screen.getByLabelText('编辑 Tech Stocks')
    await user.click(editButton)
    
    // Should populate the form with existing data
    expect(screen.getByDisplayValue('Tech Stocks')).toBeInTheDocument()
    expect(screen.getByDisplayValue('AAPL,MSFT')).toBeInTheDocument()
    
    // Modify the watchlist
    const symbolsInput = screen.getByLabelText('股票代码')
    await user.clear(symbolsInput)
    await user.type(symbolsInput, 'AAPL,MSFT,GOOGL')
    
    // Save changes
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists'),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('GOOGL')
        })
      )
    })
  })

  it('allows deleting watchlists', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] }
    ]
    
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ status: 'success' })
      })
    
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    // Wait for watchlists to load
    await waitFor(() => {
      expect(screen.getByText('Tech Stocks')).toBeInTheDocument()
    })
    
    // Click delete button
    const deleteButton = screen.getByLabelText('删除 Tech Stocks')
    await user.click(deleteButton)
    
    // Should show confirmation dialog
    expect(screen.getByText('确认删除')).toBeInTheDocument()
    expect(screen.getByText('确定要删除观察列表 "Tech Stocks" 吗？')).toBeInTheDocument()
    
    // Confirm deletion
    const confirmButton = screen.getByText('删除')
    await user.click(confirmButton)
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists/1'),
        expect.objectContaining({
          method: 'DELETE'
        })
      )
    })
    
    expect(mockProps.onWatchlistsUpdated).toHaveBeenCalled()
  })

  it('handles API errors gracefully', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Server error' })
    })
    
    render(<WatchlistDialog {...mockProps} />)
    
    // Should handle the error without crashing
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })
    
    // Error should be handled gracefully (no crash)
    expect(screen.getByText('管理观察列表')).toBeInTheDocument()
  })

  it('closes dialog when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    const closeButton = screen.getByLabelText('关闭')
    await user.click(closeButton)
    
    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('closes dialog when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<WatchlistDialog {...mockProps} />)
    
    const cancelButton = screen.getByText('取消')
    await user.click(cancelButton)
    
    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('resets form when dialog is closed and reopened', async () => {
    const user = userEvent.setup()
    const { rerender } = render(<WatchlistDialog {...mockProps} />)
    
    // Fill in some data
    const nameInput = screen.getByLabelText('观察列表名称')
    await user.type(nameInput, 'Test Name')
    
    // Close dialog
    rerender(<WatchlistDialog {...mockProps} open={false} />)
    
    // Reopen dialog
    rerender(<WatchlistDialog {...mockProps} open={true} />)
    
    // Form should be reset
    const newNameInput = screen.getByLabelText('观察列表名称')
    expect(newNameInput.value).toBe('')
  })

  it('shows loading state during operations', async () => {
    const user = userEvent.setup()
    
    // Mock a slow response
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ watchlists: [], status: 'success' })
      })
      .mockImplementationOnce(() => new Promise(resolve => {
        setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ watchlist_id: 1, status: 'success' })
        }), 100)
      }))
    
    render(<WatchlistDialog {...mockProps} />)
    
    // Fill and submit form
    const nameInput = screen.getByLabelText('观察列表名称')
    await user.type(nameInput, 'Test List')
    
    const symbolsInput = screen.getByLabelText('股票代码')
    await user.type(symbolsInput, 'AAPL')
    
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    // Should show loading state
    expect(screen.getByText('保存中...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()
    
    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText('保存中...')).not.toBeInTheDocument()
    })
  })
})
