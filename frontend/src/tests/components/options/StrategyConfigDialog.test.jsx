import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import StrategyConfigDialog from '../../../components/options/StrategyConfigDialog'
import { createMockFetch } from '../../mocks/apiMocks'

describe('StrategyConfigDialog', () => {
  const mockProps = {
    open: true,
    onClose: vi.fn(),
    selectedAccount: 1,
    strategyType: 'cash_secured_puts',
    currentConfig: {
      min_dte: 20,
      max_dte: 60,
      min_annual_roi: 0.15,
      max_delta: 0.30
    },
    onConfigSaved: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders when open', () => {
    render(<StrategyConfigDialog {...mockProps} />)

    expect(screen.getByText('策略配置')).toBeInTheDocument()
    expect(screen.getByText('现金担保看跌期权配置')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<StrategyConfigDialog {...mockProps} open={false} />)

    expect(screen.queryByText('策略配置')).not.toBeInTheDocument()
  })

  it('displays correct strategy type title', () => {
    const { rerender } = render(<StrategyConfigDialog {...mockProps} />)

    expect(screen.getByText('现金担保看跌期权配置')).toBeInTheDocument()

    rerender(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)
    expect(screen.getByText('备兑看涨期权配置')).toBeInTheDocument()

    rerender(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)
    expect(screen.getByText('铁鹰策略配置')).toBeInTheDocument()
  })

  it('loads current configuration values', () => {
    render(<StrategyConfigDialog {...mockProps} />)

    expect(screen.getByDisplayValue('20')).toBeInTheDocument() // min_dte
    expect(screen.getByDisplayValue('60')).toBeInTheDocument() // max_dte
    expect(screen.getByDisplayValue('15')).toBeInTheDocument() // min_annual_roi (as percentage)
    expect(screen.getByDisplayValue('30')).toBeInTheDocument() // max_delta (as percentage)
  })

  it('shows appropriate fields for cash secured puts strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="cash_secured_puts" />)

    expect(screen.getByLabelText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最小年化收益率 (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('最大Delta (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('最小缓冲百分比 (%)')).toBeInTheDocument()
  })

  it('shows appropriate fields for covered calls strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)

    expect(screen.getByLabelText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最小年化收益率 (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('最大Delta (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('最小上涨缓冲 (%)')).toBeInTheDocument()
  })

  it('shows appropriate fields for iron condors strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)

    expect(screen.getByLabelText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByLabelText('最小年化收益率 (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('目标看跌Delta (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('目标看涨Delta (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('最小翼展宽度')).toBeInTheDocument()
    expect(screen.getByLabelText('最大翼展宽度')).toBeInTheDocument()
  })

  it('validates input values', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    // Clear and enter invalid values
    const minDteInput = screen.getByLabelText('最小到期天数')
    await user.clear(minDteInput)
    await user.type(minDteInput, '0')

    const maxDteInput = screen.getByLabelText('最大到期天数')
    await user.clear(maxDteInput)
    await user.type(maxDteInput, '5') // Less than min_dte

    const saveButton = screen.getByText('保存')
    await user.click(saveButton)

    // Should show validation errors
    expect(screen.getByText('最小到期天数必须大于0')).toBeInTheDocument()
    expect(screen.getByText('最大到期天数必须大于最小到期天数')).toBeInTheDocument()
  })

  it('validates percentage fields', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const roiInput = screen.getByLabelText('最小年化收益率 (%)')
    await user.clear(roiInput)
    await user.type(roiInput, '150') // Over 100%

    const deltaInput = screen.getByLabelText('最大Delta (%)')
    await user.clear(deltaInput)
    await user.type(deltaInput, '-10') // Negative

    const saveButton = screen.getByText('保存')
    await user.click(saveButton)

    // Should show validation errors
    expect(screen.getByText('年化收益率必须在0-100%之间')).toBeInTheDocument()
    expect(screen.getByText('Delta必须在0-100%之间')).toBeInTheDocument()
  })

  it('saves configuration successfully', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ config_id: 1, status: 'success' })
    })

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    // Modify some values
    const minDteInput = screen.getByLabelText('最小到期天数')
    await user.clear(minDteInput)
    await user.type(minDteInput, '25')

    const roiInput = screen.getByLabelText('最小年化收益率 (%)')
    await user.clear(roiInput)
    await user.type(roiInput, '20')

    // Save configuration
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/config/cash_secured_puts'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.stringContaining('"min_dte":25')
        })
      )
    })

    expect(mockProps.onConfigSaved).toHaveBeenCalledWith(
      expect.objectContaining({
        min_dte: 25,
        min_annual_roi: 0.20
      })
    )
    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('handles save errors gracefully', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Save failed' })
    })

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存')
    await user.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })

    // Should not close dialog on error
    expect(mockProps.onClose).not.toHaveBeenCalled()
    expect(mockProps.onConfigSaved).not.toHaveBeenCalled()
  })

  it('resets to defaults when reset button is clicked', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    // Modify some values
    const minDteInput = screen.getByLabelText('最小到期天数')
    await user.clear(minDteInput)
    await user.type(minDteInput, '99')

    // Click reset button
    const resetButton = screen.getByText('重置为默认值')
    await user.click(resetButton)

    // Should reset to default values
    expect(screen.getByDisplayValue('20')).toBeInTheDocument()
  })

  it('closes dialog when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const cancelButton = screen.getByText('取消')
    await user.click(cancelButton)

    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('shows loading state during save', async () => {
    // Mock a slow response
    global.fetch = vi.fn().mockImplementation(() => new Promise(resolve => {
      setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ config_id: 1, status: 'success' })
      }), 100)
    }))

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存')
    await user.click(saveButton)

    // Should show loading state
    expect(screen.getByText('保存中...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()

    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText('保存中...')).not.toBeInTheDocument()
    })
  })

  it('handles different strategy types correctly', () => {
    const { rerender } = render(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)

    expect(screen.getByText('备兑看涨期权配置')).toBeInTheDocument()
    expect(screen.getByLabelText('最小上涨缓冲 (%)')).toBeInTheDocument()

    rerender(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)
    expect(screen.getByText('铁鹰策略配置')).toBeInTheDocument()
    expect(screen.getByLabelText('目标看跌Delta (%)')).toBeInTheDocument()
    expect(screen.getByLabelText('目标看涨Delta (%)')).toBeInTheDocument()
  })

  it('preserves form state when strategy type changes', async () => {
    const user = userEvent.setup()
    const { rerender } = render(<StrategyConfigDialog {...mockProps} />)

    // Modify a common field
    const minDteInput = screen.getByLabelText('最小到期天数')
    await user.clear(minDteInput)
    await user.type(minDteInput, '30')

    // Change strategy type
    rerender(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)

    // Common field should preserve its value
    expect(screen.getByDisplayValue('30')).toBeInTheDocument()
  })
})
