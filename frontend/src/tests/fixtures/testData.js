// Test data fixtures for consistent testing across components

export const testAccounts = [
  {
    id: 1,
    account_id: 1,
    name: 'Test Account 1',
    current_value: 10000,
    total_cost: 8000,
    total_gains: 2000,
    return_rate: 0.25,
    realized_gains: 500,
    unrealized_gains: 1500,
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    account_id: 2,
    name: 'Test Account 2',
    current_value: 5000,
    total_cost: 5500,
    total_gains: -500,
    return_rate: -0.0909,
    realized_gains: -200,
    unrealized_gains: -300,
    created_at: '2024-01-02T00:00:00Z',
  },
  {
    id: 3,
    account_id: 3,
    name: 'Empty Account',
    current_value: 0,
    total_cost: 0,
    total_gains: 0,
    return_rate: 0,
    realized_gains: 0,
    unrealized_gains: 0,
    created_at: '2024-01-03T00:00:00Z',
  },
]

export const testHoldings = [
  {
    symbol: 'AAPL',
    account_name: 'Test Account 1',
    account_id: 1,
    quantity: 10,
    avg_price: 150.0,
    current_price: 175.0,
    total_cost: 1500.0,
    current_value: 1750.0,
    gain: 250.0,
    return_rate: 16.67,
  },
  {
    symbol: 'GOOGL',
    account_name: 'Test Account 1',
    account_id: 1,
    quantity: 5,
    avg_price: 2500.0,
    current_price: 2400.0,
    total_cost: 12500.0,
    current_value: 12000.0,
    gain: -500.0,
    return_rate: -4.0,
  },
  {
    symbol: 'MSFT',
    account_name: 'Test Account 2',
    account_id: 2,
    quantity: 8,
    avg_price: 300.0,
    current_price: 350.0,
    total_cost: 2400.0,
    current_value: 2800.0,
    gain: 400.0,
    return_rate: 16.67,
  },
  {
    symbol: 'TSLA',
    account_name: 'Test Account 2',
    account_id: 2,
    quantity: 3,
    avg_price: 800.0,
    current_price: 700.0,
    total_cost: 2400.0,
    current_value: 2100.0,
    gain: -300.0,
    return_rate: -12.5,
  },
]

export const testTransactions = [
  {
    id: 1,
    account_id: 1,
    symbol: 'AAPL',
    transaction_type: 'BUY',
    quantity: 10,
    price: 150.0,
    date: '2024-01-01',
    total_amount: 1500.0,
    fees: 1.0,
  },
  {
    id: 2,
    account_id: 1,
    symbol: 'GOOGL',
    transaction_type: 'BUY',
    quantity: 5,
    price: 2500.0,
    date: '2024-01-02',
    total_amount: 12500.0,
    fees: 5.0,
  },
  {
    id: 3,
    account_id: 2,
    symbol: 'MSFT',
    transaction_type: 'BUY',
    quantity: 8,
    price: 300.0,
    date: '2024-01-03',
    total_amount: 2400.0,
    fees: 2.0,
  },
  {
    id: 4,
    account_id: 1,
    symbol: 'AAPL',
    transaction_type: 'SELL',
    quantity: 2,
    price: 180.0,
    date: '2024-01-04',
    total_amount: 360.0,
    fees: 1.0,
  },
]

export const testStrategies = {
  positions: [
    {
      symbol: 'AAPL',
      current_price: 175.0,
      avg_price: 150.0,
      quantity: 10,
      rsi: 65.5,
      recommendation: 'HOLD',
      volatility: 0.25,
      trend_strength: 0.7,
      support_level: 160.0,
      resistance_level: 185.0,
    },
    {
      symbol: 'GOOGL',
      current_price: 2400.0,
      avg_price: 2500.0,
      quantity: 5,
      rsi: 45.2,
      recommendation: 'BUY',
      volatility: 0.30,
      trend_strength: 0.6,
      support_level: 2300.0,
      resistance_level: 2600.0,
    },
    {
      symbol: 'MSFT',
      current_price: 350.0,
      avg_price: 300.0,
      quantity: 8,
      rsi: 75.8,
      recommendation: 'SELL',
      volatility: 0.20,
      trend_strength: 0.8,
      support_level: 330.0,
      resistance_level: 370.0,
    },
  ],
  summary: {
    total_positions: 3,
    buy_recommendations: 1,
    sell_recommendations: 1,
    hold_recommendations: 1,
    total_value: 50000.0,
    total_gain: 1500.0,
    overall_recommendation: 'MIXED',
  }
}

export const testMarketData = {
  hotspots: [
    {
      symbol: 'AAPL',
      price: 175.0,
      price_change: 5.25,
      percent_change: 3.1,
      volume: 50000000,
      market_cap: 2800000000000,
      sector: 'Technology',
    },
    {
      symbol: 'GOOGL',
      price: 2400.0,
      price_change: -12.50,
      percent_change: -0.5,
      volume: 25000000,
      market_cap: 1600000000000,
      sector: 'Technology',
    },
    {
      symbol: 'TSLA',
      price: 700.0,
      price_change: -25.0,
      percent_change: -3.4,
      volume: 75000000,
      market_cap: 700000000000,
      sector: 'Automotive',
    },
  ],
  fearGreedIndex: {
    value: 65,
    classification: 'Greed',
    last_update: '2024-01-01T12:00:00Z',
    previous_close: 62,
    change: 3,
  }
}

export const testChartData = [
  {
    name: 'AAPL',
    value: 1750,
    gain: 250,
    return_rate: 16.67,
    color: '#4CAF50',
  },
  {
    name: 'GOOGL',
    value: 12000,
    gain: -500,
    return_rate: -4.0,
    color: '#F44336',
  },
  {
    name: 'MSFT',
    value: 2800,
    gain: 400,
    return_rate: 16.67,
    color: '#4CAF50',
  },
]

// Form validation test data
export const testFormData = {
  validAccount: {
    name: 'Valid Test Account',
    description: 'A valid test account for testing',
  },
  invalidAccount: {
    name: '', // Empty name should be invalid
    description: 'Invalid account with empty name',
  },
  validTransaction: {
    symbol: 'AAPL',
    transaction_type: 'BUY',
    quantity: 10,
    price: 150.0,
    date: '2024-01-01',
  },
  invalidTransaction: {
    symbol: '', // Empty symbol should be invalid
    transaction_type: 'BUY',
    quantity: -5, // Negative quantity should be invalid
    price: 0, // Zero price should be invalid
    date: '',
  },
}

// Error scenarios for testing
export const testErrors = {
  networkError: new Error('Network request failed'),
  serverError: {
    status: 500,
    message: 'Internal server error',
  },
  validationError: {
    status: 400,
    message: 'Validation failed',
    details: {
      name: 'Name is required',
      quantity: 'Quantity must be positive',
    },
  },
  notFoundError: {
    status: 404,
    message: 'Resource not found',
  },
  unauthorizedError: {
    status: 401,
    message: 'Unauthorized access',
  },
}

// Helper functions for test data manipulation
export const createTestAccount = (overrides = {}) => ({
  ...testAccounts[0],
  ...overrides,
})

export const createTestHolding = (overrides = {}) => ({
  ...testHoldings[0],
  ...overrides,
})

export const createTestTransaction = (overrides = {}) => ({
  ...testTransactions[0],
  ...overrides,
})

export const getAccountById = (id) => testAccounts.find(account => account.id === id)

export const getHoldingsByAccountId = (accountId) => 
  testHoldings.filter(holding => holding.account_id === accountId)

export const getTransactionsByAccountId = (accountId) =>
  testTransactions.filter(transaction => transaction.account_id === accountId)
