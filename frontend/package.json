{"name": "stock-portfolio-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.14.0", "@mui/x-date-pickers": "^7.25.0", "axios": "^1.6.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "echarts-for-react": "^3.0.2", "puppeteer": "^24.6.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "react-window": "^1.8.11", "recharts": "^2.15.1"}, "devDependencies": {"@eslint/config-array": "^0.19.2", "@eslint/object-schema": "^2.1.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^2.1.8", "eslint": "^9.19.0", "jsdom": "^26.0.0", "vite": "^6.2.6", "vitest": "^2.1.8"}}