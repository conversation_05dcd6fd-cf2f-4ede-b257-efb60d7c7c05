"""
Sample data fixtures for testing.
"""
from datetime import datetime, timedelta
import json


# Sample account data
SAMPLE_ACCOUNTS = [
    {
        'account_id': 'ACC001',
        'account_name': '<PERSON> Individual',
        'account_type': 'Individual',
        'created_at': '2023-01-01T00:00:00'
    },
    {
        'account_id': 'ACC002',
        'account_name': 'Jane Smith IRA',
        'account_type': 'IRA',
        'created_at': '2023-01-15T00:00:00'
    },
    {
        'account_id': 'ACC003',
        'account_name': 'Family Trust',
        'account_type': 'Trust',
        'created_at': '2023-02-01T00:00:00'
    }
]

# Sample transaction data
SAMPLE_TRANSACTIONS = [
    {
        'account_id': 'ACC001',
        'symbol': 'AAPL',
        'transaction_type': 'BUY',
        'quantity': 100,
        'price': 150.00,
        'transaction_date': '2023-01-10T10:30:00',
        'fees': 1.00
    },
    {
        'account_id': 'ACC001',
        'symbol': 'GOOGL',
        'transaction_type': 'BUY',
        'quantity': 50,
        'price': 2500.00,
        'transaction_date': '2023-01-15T14:20:00',
        'fees': 1.50
    },
    {
        'account_id': 'ACC001',
        'symbol': 'AAPL',
        'transaction_type': 'SELL',
        'quantity': 25,
        'price': 155.00,
        'transaction_date': '2023-02-01T11:45:00',
        'fees': 1.00
    },
    {
        'account_id': 'ACC002',
        'symbol': 'MSFT',
        'transaction_type': 'BUY',
        'quantity': 75,
        'price': 300.00,
        'transaction_date': '2023-01-20T09:15:00',
        'fees': 1.25
    },
    {
        'account_id': 'ACC002',
        'symbol': 'TSLA',
        'transaction_type': 'BUY',
        'quantity': 30,
        'price': 800.00,
        'transaction_date': '2023-02-05T15:30:00',
        'fees': 2.00
    }
]

# Sample stock price data
SAMPLE_STOCK_PRICES = {
    'AAPL': {
        'symbol': 'AAPL',
        'current_price': 175.00,
        'previous_close': 172.50,
        'change': 2.50,
        'change_percent': 1.45,
        'volume': ********,
        'market_cap': *************,
        'pe_ratio': 28.5,
        'dividend_yield': 0.0045
    },
    'GOOGL': {
        'symbol': 'GOOGL',
        'current_price': 2650.00,
        'previous_close': 2625.00,
        'change': 25.00,
        'change_percent': 0.95,
        'volume': 1200000,
        'market_cap': 1********0000,
        'pe_ratio': 25.2,
        'dividend_yield': 0.0000
    },
    'MSFT': {
        'symbol': 'MSFT',
        'current_price': 325.00,
        'previous_close': 320.00,
        'change': 5.00,
        'change_percent': 1.56,
        'volume': ********,
        'market_cap': *************,
        'pe_ratio': 30.1,
        'dividend_yield': 0.0072
    },
    'TSLA': {
        'symbol': 'TSLA',
        'current_price': 850.00,
        'previous_close': 845.00,
        'change': 5.00,
        'change_percent': 0.59,
        'volume': ********,
        'market_cap': 8********000,
        'pe_ratio': 85.5,
        'dividend_yield': 0.0000
    }
}

# Sample portfolio data
SAMPLE_PORTFOLIO = [
    {
        'account_id': 'ACC001',
        'symbol': 'AAPL',
        'quantity': 75,  # 100 bought - 25 sold
        'avg_cost': 150.00,
        'current_price': 175.00,
        'market_value': 13125.00,
        'unrealized_gain': 1875.00,
        'unrealized_gain_pct': 16.67
    },
    {
        'account_id': 'ACC001',
        'symbol': 'GOOGL',
        'quantity': 50,
        'avg_cost': 2500.00,
        'current_price': 2650.00,
        'market_value': 132500.00,
        'unrealized_gain': 7500.00,
        'unrealized_gain_pct': 6.00
    },
    {
        'account_id': 'ACC002',
        'symbol': 'MSFT',
        'quantity': 75,
        'avg_cost': 300.00,
        'current_price': 325.00,
        'market_value': 24375.00,
        'unrealized_gain': 1875.00,
        'unrealized_gain_pct': 8.33
    },
    {
        'account_id': 'ACC002',
        'symbol': 'TSLA',
        'quantity': 30,
        'avg_cost': 800.00,
        'current_price': 850.00,
        'market_value': 25500.00,
        'unrealized_gain': 1500.00,
        'unrealized_gain_pct': 6.25
    }
]

# Sample analytics data
SAMPLE_ANALYTICS = {
    'total_portfolio_value': 195500.00,
    'total_unrealized_gain': 12750.00,
    'total_unrealized_gain_pct': 6.98,
    'total_realized_gain': 125.00,  # From AAPL sale: (155-150)*25 - fees
    'account_breakdown': [
        {
            'account_id': 'ACC001',
            'account_name': 'John Doe Individual',
            'total_value': 145625.00,
            'unrealized_gain': 9375.00,
            'unrealized_gain_pct': 6.88
        },
        {
            'account_id': 'ACC002',
            'account_name': 'Jane Smith IRA',
            'total_value': 49875.00,
            'unrealized_gain': 3375.00,
            'unrealized_gain_pct': 7.26
        }
    ],
    'top_performers': [
        {'symbol': 'AAPL', 'gain_pct': 16.67},
        {'symbol': 'MSFT', 'gain_pct': 8.33},
        {'symbol': 'TSLA', 'gain_pct': 6.25},
        {'symbol': 'GOOGL', 'gain_pct': 6.00}
    ]
}

# Sample market hotspots data
SAMPLE_MARKET_HOTSPOTS = {
    'gainers': [
        {'symbol': 'NVDA', 'change_pct': 8.5, 'price': 450.00},
        {'symbol': 'AMD', 'change_pct': 6.2, 'price': 125.00},
        {'symbol': 'AAPL', 'change_pct': 1.45, 'price': 175.00}
    ],
    'losers': [
        {'symbol': 'META', 'change_pct': -3.2, 'price': 320.00},
        {'symbol': 'NFLX', 'change_pct': -2.8, 'price': 380.00},
        {'symbol': 'AMZN', 'change_pct': -1.5, 'price': 3200.00}
    ],
    'most_active': [
        {'symbol': 'SPY', 'volume': *********},
        {'symbol': 'QQQ', 'volume': ********},
        {'symbol': 'AAPL', 'volume': ********}
    ]
}

# Sample LLM analysis response
SAMPLE_LLM_ANALYSIS = {
    "overall_sentiment": "POSITIVE",
    "risk_level": "MEDIUM",
    "portfolio_score": 7.5,
    "analysis": "The portfolio shows strong diversification across technology stocks with good growth potential.",
    "recommendations": [
        "Consider taking profits on AAPL given the strong performance",
        "Monitor TSLA volatility and consider position sizing",
        "Good diversification across large-cap tech stocks"
    ],
    "risk_factors": [
        "High concentration in technology sector",
        "Market volatility in growth stocks",
        "Interest rate sensitivity"
    ],
    "opportunities": [
        "Potential for continued growth in AI and cloud computing",
        "Strong fundamentals in core holdings",
        "Good entry points for additional positions"
    ]
}

# Sample technical indicators
SAMPLE_TECHNICAL_INDICATORS = {
    'AAPL': {
        'rsi': 65.5,
        'macd': 2.3,
        'macd_signal': 1.8,
        'macd_histogram': 0.5,
        'sma_20': 170.0,
        'sma_50': 165.0,
        'bollinger_upper': 180.0,
        'bollinger_lower': 160.0
    },
    'GOOGL': {
        'rsi': 58.2,
        'macd': 15.6,
        'macd_signal': 12.3,
        'macd_histogram': 3.3,
        'sma_20': 2600.0,
        'sma_50': 2550.0,
        'bollinger_upper': 2700.0,
        'bollinger_lower': 2500.0
    }
}

def get_sample_data(data_type: str):
    """Get sample data by type."""
    data_map = {
        'accounts': SAMPLE_ACCOUNTS,
        'transactions': SAMPLE_TRANSACTIONS,
        'stock_prices': SAMPLE_STOCK_PRICES,
        'portfolio': SAMPLE_PORTFOLIO,
        'analytics': SAMPLE_ANALYTICS,
        'market_hotspots': SAMPLE_MARKET_HOTSPOTS,
        'llm_analysis': SAMPLE_LLM_ANALYSIS,
        'technical_indicators': SAMPLE_TECHNICAL_INDICATORS
    }
    return data_map.get(data_type, {})
