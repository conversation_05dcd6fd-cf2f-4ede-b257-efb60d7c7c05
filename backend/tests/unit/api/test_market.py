"""
Unit tests for the market API endpoints.
"""
import pytest
import json
from unittest.mock import patch, Mo<PERSON>, MagicMock
import requests
from tests.utils.test_helpers import DatabaseTestHelper, APITestHelper


class TestMarketAPI:
    """Test cases for market API endpoints."""
    
    def test_get_market_hotspots_empty_database(self, client, test_db):
        """Test getting market hotspots when database is empty."""
        response = client.get('/api/market/hotspots')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert 'gainers' in data
        assert 'losers' in data
        assert data['gainers'] == []
        assert data['losers'] == []
    
    def test_get_market_hotspots_no_holdings(self, client, test_db):
        """Test getting market hotspots when no holdings exist."""
        # Create account but no transactions
        conn = DatabaseTestHelper.get_connection(test_db)
        DatabaseTestHelper.create_test_account(conn)
        conn.close()
        
        response = client.get('/api/market/hotspots')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert data['gainers'] == []
        assert data['losers'] == []
    
    def test_get_market_hotspots_with_holdings(self, client, test_db):
        """Test getting market hotspots with actual holdings and price data."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        
        # Create transactions for multiple stocks
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data['account_id']), 
            symbol='AAPL', quantity=100, price=150.0
        )
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data['account_id']), 
            symbol='MSFT', quantity=50, price=200.0
        )
        
        # Add price data with different change percentages
        cursor = conn.cursor()
        # AAPL: current 175, previous 150 = +16.67% (gainer)
        cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                      ('AAPL', '2024-01-01', 150.0))
        cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                      ('AAPL', '2024-01-02', 175.0))
        
        # MSFT: current 180, previous 200 = -10% (loser)
        cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                      ('MSFT', '2024-01-01', 200.0))
        cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                      ('MSFT', '2024-01-02', 180.0))
        
        conn.commit()
        conn.close()
        
        response = client.get('/api/market/hotspots')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert 'gainers' in data
        assert 'losers' in data
        assert len(data['gainers']) >= 1
        assert len(data['losers']) >= 1
        
        # Check structure of returned data
        if data['gainers']:
            gainer = data['gainers'][0]
            assert 'symbol' in gainer
            assert 'name' in gainer
            assert 'price' in gainer
            assert 'change_percent' in gainer
            assert 'quantity' in gainer
            assert 'avg_cost' in gainer
            assert 'total_value' in gainer
    
    @patch('src.api.market.calculate_current_market_value')
    def test_get_market_hotspots_calculation_error(self, mock_calc, client, test_db):
        """Test market hotspots when calculation function raises an error."""
        mock_calc.side_effect = Exception("Calculation error")
        
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        DatabaseTestHelper.create_test_transaction(
            conn, account_id=str(account_data['account_id']), 
            symbol='AAPL', quantity=100, price=150.0
        )
        
        # Add price data
        cursor = conn.cursor()
        cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                      ('AAPL', '2024-01-02', 175.0))
        conn.commit()
        conn.close()
        
        response = client.get('/api/market/hotspots')
        data = APITestHelper.assert_json_response(response, 200)
        
        # Should return empty lists when calculation fails
        assert data['gainers'] == []
        assert data['losers'] == []
    
    def test_get_market_hotspots_database_error(self, client, test_db):
        """Test market hotspots endpoint with database error."""
        with patch('src.api.market.get_db_connection') as mock_conn:
            mock_conn.side_effect = Exception("Database connection failed")
            
            response = client.get('/api/market/hotspots')
            APITestHelper.assert_error_response(response, 500)
    
    def test_get_market_hotspots_sorting(self, client, test_db):
        """Test that market hotspots are properly sorted by change percentage."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        
        # Create multiple stocks with different performance
        stocks = [
            ('AAPL', 100, 150.0, 175.0),  # +16.67%
            ('MSFT', 50, 200.0, 180.0),   # -10%
            ('GOOGL', 25, 100.0, 110.0),  # +10%
        ]
        
        cursor = conn.cursor()
        for symbol, quantity, old_price, new_price in stocks:
            DatabaseTestHelper.create_test_transaction(
                conn, account_id=str(account_data['account_id']), 
                symbol=symbol, quantity=quantity, price=old_price
            )
            cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                          (symbol, '2024-01-01', old_price))
            cursor.execute("INSERT INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)", 
                          (symbol, '2024-01-02', new_price))
        
        conn.commit()
        conn.close()
        
        response = client.get('/api/market/hotspots')
        data = APITestHelper.assert_json_response(response, 200)
        
        # Gainers should be sorted by highest change_percent first
        if len(data['gainers']) > 1:
            for i in range(len(data['gainers']) - 1):
                assert data['gainers'][i]['change_percent'] >= data['gainers'][i + 1]['change_percent']
        
        # Losers should be sorted by lowest change_percent first
        if len(data['losers']) > 1:
            for i in range(len(data['losers']) - 1):
                assert data['losers'][i]['change_percent'] <= data['losers'][i + 1]['change_percent']
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_success(self, mock_get, client, test_db):
        """Test successful fear and greed index retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'fear_and_greed': {
                'score': 45.7,
                'rating': 'Fear',
                'timestamp': '2024-01-01T12:00:00Z'
            }
        }
        mock_get.return_value = mock_response
        
        response = client.get('/api/market/fear-greed-index')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert 'value' in data
        assert 'description' in data
        assert 'timestamp' in data
        assert data['value'] == 46  # Should be rounded
        assert data['description'] == 'Fear'
        assert data['timestamp'] == '2024-01-01T12:00:00Z'
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_http_error(self, mock_get, client, test_db):
        """Test fear and greed index with HTTP error."""
        # Mock HTTP error
        mock_response = Mock()
        mock_response.status_code = 418
        mock_get.return_value = mock_response
        mock_get.side_effect = requests.exceptions.HTTPError(response=mock_response)
        
        response = client.get('/api/market/fear-greed-index')
        APITestHelper.assert_error_response(response, 418)
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_connection_error(self, mock_get, client, test_db):
        """Test fear and greed index with connection error."""
        # Mock connection error
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        response = client.get('/api/market/fear-greed-index')
        APITestHelper.assert_error_response(response, 500)
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_timeout(self, mock_get, client, test_db):
        """Test fear and greed index with timeout."""
        # Mock timeout error
        mock_get.side_effect = requests.exceptions.Timeout("Request timed out")
        
        response = client.get('/api/market/fear-greed-index')
        APITestHelper.assert_error_response(response, 500)
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_json_decode_error(self, mock_get, client, test_db):
        """Test fear and greed index with invalid JSON response."""
        # Mock successful HTTP response but invalid JSON
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_response.text = "Invalid JSON response"
        mock_get.return_value = mock_response
        
        response = client.get('/api/market/fear-greed-index')
        APITestHelper.assert_error_response(response, 500)
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_missing_data(self, mock_get, client, test_db):
        """Test fear and greed index with missing data fields."""
        # Mock response with missing fields
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'fear_and_greed': {
                'score': None,  # Missing score
                'rating': 'Fear'
                # Missing timestamp
            }
        }
        mock_get.return_value = mock_response
        
        response = client.get('/api/market/fear-greed-index')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert data['value'] is None
        assert data['description'] == 'Fear'
        assert data['timestamp'] is None
    
    @patch('src.api.market.requests.get')
    def test_get_fear_greed_index_non_numeric_score(self, mock_get, client, test_db):
        """Test fear and greed index with non-numeric score."""
        # Mock response with non-numeric score
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'fear_and_greed': {
                'score': 'invalid',
                'rating': 'Fear',
                'timestamp': '2024-01-01T12:00:00Z'
            }
        }
        mock_get.return_value = mock_response
        
        response = client.get('/api/market/fear-greed-index')
        data = APITestHelper.assert_json_response(response, 200)
        
        assert data['value'] is None  # Should handle non-numeric gracefully
        assert data['description'] == 'Fear'
    
    def test_market_endpoints_methods(self, client, test_db):
        """Test that only GET methods are allowed on market endpoints."""
        endpoints = [
            '/api/market/hotspots',
            '/api/market/fear-greed-index'
        ]
        
        for endpoint in endpoints:
            # GET should work
            response = client.get(endpoint)
            assert response.status_code in [200, 500]  # Should not be 405
            
            # Other methods should return 405
            response = client.post(endpoint)
            assert response.status_code == 405
            
            response = client.put(endpoint)
            assert response.status_code == 405
            
            response = client.delete(endpoint)
            assert response.status_code == 405
