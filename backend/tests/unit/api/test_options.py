"""
Comprehensive test suite for options analysis API endpoints.

Tests all API endpoints for options analysis including:
- Watchlist management endpoints
- Strategy configuration endpoints
- Options analysis endpoints
- Data refresh endpoints
- Error handling and validation
"""

import pytest
import json
import pandas as pd
from datetime import datetime
from unittest.mock import Mock, patch
import tempfile
import sqlite3

from tests.utils.test_helpers import DatabaseTestHelper, APITestHelper


@pytest.mark.unit
@pytest.mark.api
@pytest.mark.options
class TestOptionsAPI:
    """Test the options analysis API endpoints."""

    @pytest.fixture
    def sample_account(self, test_db):
        """Create a test account and return its ID."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        conn.close()
        return account_data['account_id']

    # Watchlist Management API Tests
    
    def test_get_watchlists_empty(self, client, sample_account):
        """Test getting watchlists when none exist."""
        response = client.get(f'/api/strategies/options/watchlists?account_id={sample_account}')
        data = APITestHelper.assert_json_response(response, 200)
        assert 'watchlists' in data
        assert data['status'] == 'success'
        assert data['watchlists'] == []

    def test_get_watchlists_missing_account_id(self, client):
        """Test getting watchlists without account_id parameter."""
        response = client.get('/api/strategies/options/watchlists')
        APITestHelper.assert_error_response(response, 400, 'Account ID is required')

    def test_create_watchlist_success(self, client, sample_account):
        """Test successful watchlist creation."""
        watchlist_data = {
            'account_id': sample_account,
            'name': 'Tech Stocks',
            'symbols': ['AAPL', 'MSFT', 'GOOGL']
        }

        response = client.post(
            '/api/strategies/options/watchlists',
            data=json.dumps(watchlist_data),
            content_type='application/json'
        )

        data = APITestHelper.assert_json_response(response, 200)
        assert 'watchlist_id' in data
        assert data['status'] == 'success'
        assert isinstance(data['watchlist_id'], int)
        assert data['watchlist_id'] > 0

    def test_create_watchlist_missing_account_id(self, client):
        """Test creating watchlist without account_id."""
        watchlist_data = {
            'name': 'Tech Stocks',
            'symbols': ['AAPL', 'MSFT']
        }

        response = client.post(
            '/api/strategies/options/watchlists',
            data=json.dumps(watchlist_data),
            content_type='application/json'
        )

        APITestHelper.assert_error_response(response, 400, 'Account ID is required')

    def test_create_watchlist_empty_symbols(self, client, sample_account):
        """Test creating watchlist with empty symbols list."""
        watchlist_data = {
            'account_id': sample_account,
            'name': 'Empty List',
            'symbols': []
        }

        response = client.post(
            '/api/strategies/options/watchlists',
            data=json.dumps(watchlist_data),
            content_type='application/json'
        )

        APITestHelper.assert_error_response(response, 400, 'cannot be empty')

    # Strategy Configuration API Tests
    
    def test_get_strategy_config_default(self, client, sample_account):
        """Test getting default strategy configuration."""
        strategy_type = 'cash_secured_puts'
        response = client.get(
            f'/api/strategies/options/config/{strategy_type}?account_id={sample_account}'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'config' in data
        assert data['strategy_type'] == strategy_type
        assert data['status'] == 'success'
        
        # Should have default config values
        config = data['config']
        assert 'min_dte' in config
        assert 'max_dte' in config
        assert 'min_annual_roi' in config
    
    def test_get_strategy_config_invalid_strategy(self, client, sample_account):
        """Test getting configuration for invalid strategy type."""
        invalid_strategy = 'invalid_strategy_type'
        response = client.get(
            f'/api/strategies/options/config/{invalid_strategy}?account_id={sample_account}'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Invalid strategy type' in data['error']
    
    def test_save_strategy_config_success(self, client, sample_account):
        """Test successful strategy configuration save."""
        strategy_type = 'covered_calls'
        config_data = {
            'account_id': sample_account,
            'config': {
                'min_dte': 25,
                'max_dte': 50,
                'min_annual_roi': 0.12,
                'max_delta': 0.25
            }
        }
        
        response = client.post(
            f'/api/strategies/options/config/{strategy_type}',
            data=json.dumps(config_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'config_id' in data
        assert data['status'] == 'success'
        assert isinstance(data['config_id'], int)

    # Options Analysis API Tests
    
    @patch('src.api.strategies.options_data_manager.fetch_data_for_batch')
    @patch('src.api.strategies.options_data_manager.get_market_conditions')
    def test_analyze_options_success(self, mock_market_conditions, mock_fetch_data, client, sample_account):
        """Test successful options analysis."""
        # Mock data fetching
        mock_options_df = pd.DataFrame({
            'symbol': ['AAPL', 'AAPL', 'AAPL'],
            'optionType': ['puts', 'puts', 'calls'],
            'strike': [145.0, 140.0, 155.0],
            'expiration': ['2024-02-16', '2024-02-16', '2024-02-16'],
            'dte': [30, 30, 30],
            'bid': [3.0, 2.0, 2.5],
            'ask': [3.5, 2.5, 3.0],
            'impliedVolatility': [0.25, 0.28, 0.23],
            'volume': [100, 80, 90],
            'openInterest': [500, 400, 450]
        })
        mock_prices = {'AAPL': 150.0}
        mock_fetch_data.return_value = (mock_options_df, mock_prices)
        
        mock_market_conditions.return_value = {
            'volatility_regime': 'Normal',
            'stress_indicator': 45.0,
            'timestamp': datetime.now().isoformat()
        }
        
        analysis_data = {
            'account_id': sample_account,
            'strategy_type': 'cash_secured_puts',
            'symbols': ['AAPL']
        }
        
        response = client.post(
            '/api/strategies/options/analyze',
            data=json.dumps(analysis_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'candidates' in data
        assert 'market_conditions' in data
        assert 'config_used' in data
        assert data['status'] == 'success'
