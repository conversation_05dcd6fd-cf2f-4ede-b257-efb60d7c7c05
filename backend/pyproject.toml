[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "options-analysis-backend"
version = "1.0.0"
description = "Options trading analysis backend service"
authors = [
    {name = "Options Analysis Team"}
]
dependencies = [
    "flask>=3.0.0",
    "flask-cors>=4.0.0",
    "pandas>=2.2.0",
    "numpy>=1.24.0",
    "yahooquery>=2.4.1",
    "scipy>=1.11.0",
    "python-dateutil>=2.8.0",
    "requests>=2.31.0",
    "pandas-ta>=0.3.14b0"
]
requires-python = ">=3.8"

[tool.pytest.ini_options]
# Test discovery
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Default options
addopts = [
    "--verbose",
    "--cov=src",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing", 
    "--cov-report=xml",
    "--cov-fail-under=90",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--durations=10",
    "-ra"
]

# Test markers for categorization and filtering
markers = [
    "unit: Unit tests for individual functions/methods",
    "integration: Integration tests across multiple components", 
    "slow: Slow running tests (>1 second)",
    "api: API endpoint tests",
    "database: Database related tests",
    "external: Tests that require external services (should be mocked)",
    "options: Options analysis specific tests",
    "strategies: Strategy analysis tests", 
    "data: Data fetching and management tests",
    "watchlist: Watchlist management tests",
    "config: Configuration management tests"
]

# Warning filters
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore:pkg_resources is deprecated:UserWarning",
    "ignore::UserWarning:yahooquery",
    "ignore::FutureWarning:pandas"
]

# Minimum pytest version
minversion = "6.0"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:"
]
