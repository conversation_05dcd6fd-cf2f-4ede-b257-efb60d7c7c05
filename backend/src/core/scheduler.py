import schedule
import time
import sqlite3
import yfinance as yf
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import threading
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception
import atexit
from ..utils.logger import log_exceptions, get_logger
import os
from yfinance.exceptions import YFRateLimitError
import random
import sys 
from exchange_calendars import get_calendar

# Ensure the backend directory is in the Python path to resolve model imports
BACKEND_PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if BACKEND_PROJECT_ROOT not in sys.path:
    sys.path.append(BACKEND_PROJECT_ROOT)

from ..models.prediction_model.prediction_pipeline import run_training_pipeline_for_tickers

# Define the retry condition function
def is_rate_limit_error(exception):
    """Return True if the exception is a YFRateLimitError or contains 'Too Many Requests'."""
    return (
        isinstance(exception, YFRateLimitError)
    ) or (
        "Too Many Requests" in str(exception)
    )

class StockDataManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
            return cls._instance

    @log_exceptions
    def __init__(self, db_path=None, cache_dir=None):
        if not hasattr(self, 'initialized'):
            if db_path is None:
                self.db_path = os.path.join(BACKEND_PROJECT_ROOT, '..', 'data', 'stock_trading.db')
            else:
                self.db_path = db_path
            if cache_dir is None:
                cache_dir = os.path.join(BACKEND_PROJECT_ROOT, '..', 'data', 'cache')
            self.cache_dir = Path(cache_dir)
            self.cache_dir.mkdir(exist_ok=True)
            self.scheduler_thread = None
            self.stop_flag = threading.Event()
            self.initialized = True
            self.logger = get_logger("StockDataManager")
            self.logger.info(f"Initialized StockDataManager with DB: {self.db_path}, Cache: {self.cache_dir}")
            self.model_retrainer = ModelRetrainingScheduler(db_path=self.db_path, logger_instance=self.logger)

    def get_db_connection(self):
        return sqlite3.connect(self.db_path)

    def get_all_tickers(self, conn=None):
        """从数据库获取所有唯一的股票代码，排除0持仓的股票"""
        close_conn = False
        if conn is None:
            conn = self.get_db_connection()
            close_conn = True
        
        try:
            cursor = conn.execute('''
                SELECT DISTINCT symbol 
                FROM transactions 
                WHERE symbol != 'CASH'
                GROUP BY symbol
                HAVING SUM(quantity) > 1e-9
            ''')
            # Clean symbols by removing spaces and special characters
            return [row[0].strip().replace('"', '') for row in cursor.fetchall()]
        finally:
            if close_conn:
                conn.close()

    def should_update_cache(self, symbol):
        """检查是否需要更新缓存文件（基于时间）"""
        cache_file = self.cache_dir / f"{symbol}_data.parquet"
        
        # 如果文件不存在，需要更新
        if not cache_file.exists():
            self.logger.info(f"Cache file for {symbol} does not exist")
            return True

        # 如果文件存在但超过12小时，需要更新
        file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
        if datetime.now() - file_time > timedelta(hours=12):
            self.logger.info(f"Cache file for {symbol} is older than 12 hours")
            return True

        self.logger.debug(f"Cache file for {symbol} is up to date.")
        return False

    def _fetch_real_time_price_only(self, symbol: str) -> float | None:
        """使用yfinance获取实时价格，应用重试逻辑。只返回 real_time_price。"""
        self.logger.info(f"Fetching real-time price from yfinance for {symbol}")
        real_time_price = None
        try:
            ticker = yf.Ticker(symbol)
            # 获取实时价格
            info = ticker.info
            real_time_price = info.get('currentPrice') or info.get('regularMarketPrice')
            
            if real_time_price:
                 self.logger.info(f"Fetched real-time price for {symbol}: {real_time_price}")
            else:
                 # Attempt fallback using history for latest close if info fails
                 try:
                     hist = ticker.history(period="1d")
                     if not hist.empty:
                         fallback_price = hist['Close'].iloc[-1]
                         self.logger.info(f"Using fallback price (1d history close) for {symbol}: {fallback_price}")
                         real_time_price = fallback_price
                     else:
                         self.logger.warning(f"Fallback using 1d history also failed for {symbol}")
                 except Exception as hist_e:
                     self.logger.warning(f"Error fetching 1d history for fallback price for {symbol}: {hist_e}")

            return real_time_price

        except Exception as e:
            # Check if it's the rate limit error
            if isinstance(e, YFRateLimitError):
                self.logger.warning(f"Rate limit error encountered fetching real-time for {symbol}. Propagating for retry...")
                raise e # Re-raise to let the @retry decorator handle it
            else:
                # Handle other non-retryable exceptions during real-time fetch
                self.logger.error(f"Non-retryable error fetching real-time price for {symbol}: {str(e)}")
                return None # Return None on other errors

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=2, min=5, max=60),
        retry=retry_if_exception(is_rate_limit_error),
        retry_error_callback=lambda retry_state: retry_state.args[0].logger.warning(
            f"Fetching data for {retry_state.args[1] if len(retry_state.args) > 1 else 'symbol'} failed after multiple retries due to rate limits: {retry_state.outcome.exception()}"
        )
    )
    def _fetch_yfinance_data(self, symbol: str):
        """使用yfinance获取历史和实时数据，应用重试逻辑。返回 (historical_data, real_time_price)"""
        self.logger.info(f"Fetching data from yfinance for {symbol}")
        
        # 首先尝试从现有缓存读取数据，以备回退
        cache_file = self.cache_dir / f"{symbol}_data.parquet"
        existing_data = None
        if cache_file.exists():
            try:
                existing_data = pd.read_parquet(cache_file)
            except Exception as e:
                self.logger.error(f"Error reading existing cache for {symbol}: {str(e)}")

        try:
            ticker = yf.Ticker(symbol)
            
            # 获取实时价格
            real_time_price = ticker.info.get('currentPrice') or ticker.info.get('regularMarketPrice')
            if real_time_price:
                 self.logger.info(f"Fetched real-time price for {symbol}: {real_time_price}")
            else:
                 self.logger.warning(f"Could not fetch real-time price for {symbol}")
            # Try downloading with progressively shorter periods if full year fails
            data = None
            hist_data = ticker.history(period="1y")
            if not hist_data.empty:
                self.logger.info(f"Successfully downloaded 1y historical data for {symbol}")
                data = hist_data
            else:
                # If all periods failed but we have existing data, use that
                if existing_data is not None and not existing_data.empty:
                    self.logger.warning(f"Using existing cache for {symbol} as all download attempts failed")
                    data = existing_data
                else:
                    self.logger.warning(f"No historical data could be fetched or found in cache for {symbol}")
                    # Return only real-time price if available, otherwise None
                    return None, real_time_price 

            if data.empty and existing_data is not None and not existing_data.empty:
                self.logger.warning(f"Using existing cache for {symbol} as new data is empty")
                data = existing_data
            elif data.empty:
                 self.logger.warning(f"No historical data available for {symbol}")
                 # Return only real-time price if available, otherwise None
                 return None, real_time_price

            return data, real_time_price

        except Exception as e:
            if isinstance(e, YFRateLimitError):
                self.logger.warning(f"Rate limit error encountered for {symbol}. Propagating for retry...")
                raise e
            else:
                # Handle other exceptions as before
                self.logger.error(f"Non-retryable error fetching data from yfinance for {symbol}: {str(e)}")
                # Fallback to existing data if available
                if existing_data is not None and not existing_data.empty:
                     self.logger.warning(f"Falling back to existing historical data for {symbol}")
                     return existing_data, None # Assume RT price fetch also likely failed
                else:
                     return None, None # Return None if other error and no cache

    def get_latest_db_price(self, symbol: str, conn=None) -> tuple[str | None, float | None]:
        """获取数据库中特定股票的最新缓存价格和日期"""
        close_conn = False
        if conn is None:
            conn = self.get_db_connection()
            close_conn = True
        
        try:
            cursor = conn.execute("""
                SELECT date, close 
                FROM cached_prices 
                WHERE symbol = ? 
                ORDER BY date DESC 
                LIMIT 1
            """, (symbol,))
            result = cursor.fetchone()
            if result:
                return result[0], result[1]
            else:
                return None, None
        except Exception as e:
            self.logger.error(f"Error fetching latest DB price for {symbol}: {str(e)}")
            return None, None
        finally:
            if close_conn:
                conn.close()

    def check_random_symbol_price(self):
        """随机检查一个股票的价格，如果不一致则触发全面更新"""
        self.logger.info("Running random symbol price check...")
        conn = self.get_db_connection()
        symbol_to_check = None
        try:
            tickers = self.get_all_tickers(conn)
            if not tickers:
                self.logger.info("No tickers found in transactions to check.")
                return

            symbol_to_check = random.choice(tickers)
            self.logger.info(f"Checking price for randomly selected symbol: {symbol_to_check}")

            # Fetch *only* real-time price using the new leaner method
            real_time_price = self._fetch_real_time_price_only(symbol_to_check)

            if real_time_price is None:
                self.logger.warning(f"Could not fetch real-time price for {symbol_to_check} (after retries). Skipping comparison.")
                return

            # Fetch latest price from DB
            db_date, db_price = self.get_latest_db_price(symbol_to_check, conn)
            today = datetime.now().strftime('%Y-%m-%d')

            trigger_full_update = False
            price_is_different = db_price is None or abs(real_time_price - db_price) > 1e-6

            if not is_market_open_today():
                self.logger.info("Market is closed today.")
                if price_is_different:
                    self.logger.info(f"Price for {symbol_to_check} is different from DB. DB: {db_price}, RT: {real_time_price}. Triggering update.")
                    trigger_full_update = True
                else:
                    self.logger.info(f"Price for {symbol_to_check} is same as DB. Skipping update.")
            else: # Market is open
                self.logger.info("Market is open today.")
                if db_price is None:
                    self.logger.info(f"No price found in DB for {symbol_to_check}. Triggering full update.")
                    trigger_full_update = True
                elif db_date != today:
                    self.logger.info(f"DB price for {symbol_to_check} is from {db_date} (not today). Triggering full update.")
                    trigger_full_update = True
                elif price_is_different:
                    self.logger.info(f"Price for {symbol_to_check} is different from DB. DB: {db_price}, RT: {real_time_price}. Triggering update.")
                    trigger_full_update = True
                else:
                    self.logger.info(f"Price check passed for {symbol_to_check}. DB price ({db_price} on {db_date}) is close enough to real-time ({real_time_price}).")

            if trigger_full_update:
                self.logger.warning(f"Discrepancy detected for {symbol_to_check}, initiating full stock data update.")
                # Run the full update in a separate thread to avoid blocking the scheduler
                update_thread = threading.Thread(target=self.update_all_stocks, daemon=True)
                update_thread.start()
        except Exception as e:
            symbol_name = symbol_to_check if symbol_to_check else "unknown symbol"
            self.logger.error(f"Error during random symbol check for {symbol_name}: {str(e)}", exc_info=True)
        finally:
            if conn:
                conn.close()

    def update_stock_data(self, symbol: str, conn):
        """
        更新单个股票的数据。
        - 如果市场休市，则跳过所有网络请求。
        - 如果Parquet缓存文件是新的，则只获取实时价格来更新数据库。
        - 如果Parquet缓存文件已过期或不存在，则获取完整的历史数据和实时价格。
        """
        try:
            symbol = symbol.strip().replace('"', '')
            today = datetime.now().strftime('%Y-%m-%d')
            cache_file = self.cache_dir / f"{symbol}_data.parquet"

            # 2. 决定是仅获取实时价格还是获取全部数据
            historical_data = None
            real_time_price = None

            if self.should_update_cache(symbol):
                self.logger.info(f"Cache for {symbol} requires update. Fetching full historical data.")
                historical_data, real_time_price = self._fetch_yfinance_data(symbol)
            else:
                self.logger.info(f"Cache for {symbol} is recent. Fetching real-time price only.")
                real_time_price = self._fetch_real_time_price_only(symbol)

            # 3. 获取数据库中今天的价格，以便比较
            db_price_today = None
            try:
                cursor = conn.execute('SELECT close FROM cached_prices WHERE symbol = ? AND date = ?', (symbol, today))
                result = cursor.fetchone()
                if result:
                    db_price_today = result[0]
            except Exception as e:
                 self.logger.error(f"Error checking for existing price for {symbol}: {e}")

            # 4. Process results and update DB/cache
            try:
                # --- Update DB only if real-time price is available and different ---
                if real_time_price is not None:
                    if db_price_today is None or abs(real_time_price - db_price_today) > 1e-6:
                        self.logger.info(f"Updating DB for {symbol}. Real-time price ({real_time_price}) differs from DB price ({db_price_today}) for today ({today}).")
                        conn.execute(
                            'INSERT OR REPLACE INTO cached_prices (symbol, date, close) VALUES (?, ?, ?)',
                            (symbol, today, real_time_price)
                        )
                        conn.commit()
                    else:
                        self.logger.info(f"DB price for {symbol} ({db_price_today}) on {today} matches real-time ({real_time_price}). No DB update needed.")
                else:
                    self.logger.warning(f"No real-time price fetched for {symbol}. DB cannot be updated with real-time data.")

                # --- Update Parquet cache if full historical data was fetched ---
                if historical_data is not None and not historical_data.empty:
                    try:
                        historical_data.to_parquet(cache_file)
                        self.logger.info(f"Successfully cached historical data for {symbol} (Reason: Initial fetch or cache expired).")
                    except Exception as e:
                        self.logger.error(f"Error saving cache file for {symbol}: {str(e)}")
                else:
                    if self.should_update_cache(symbol):
                         self.logger.warning(f"No historical data fetched for {symbol}, cannot update Parquet cache even though it was required.")

            except Exception as e:
                self.logger.error(f"Error processing data or updating database/cache for {symbol}: {str(e)}")

        except Exception as e:
            self.logger.error(f"Overall error updating data for {symbol}: {str(e)}")

    def update_all_stocks(self):
        """更新所有股票的数据"""
        conn = self.get_db_connection()
        try:
            tickers = self.get_all_tickers(conn)
            self.logger.info(f"Starting full update for {len(tickers)} tickers.")
            for symbol in tickers:
                self.update_stock_data(symbol, conn)
                time.sleep(5) # Be respectful to the API provider
            self.logger.info("Finished full stock update.")
        except Exception as e:
            self.logger.error(f"An error occurred during update_all_stocks: {e}", exc_info=True)
        finally:
            if conn:
                conn.close()
            self.logger.info("Database connection closed after full update.")
        

    def start_scheduler(self):
        """启动调度器线程"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.logger.info("Scheduler is already running")
            return

        self.stop_flag.clear()
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("Scheduler thread started")

        # 注册退出时的清理函数
        atexit.register(self.stop_scheduler)

    def stop_scheduler(self):
        """停止调度器线程"""
        if self.scheduler_thread:
            self.logger.info("Stopping scheduler...")
            self.stop_flag.set()
            self.scheduler_thread.join(timeout=5)
            self.logger.info("Scheduler stopped")

    def _run_job_in_thread(self, job_func, *args, **kwargs):
        """Helper to run a job in a new daemon thread."""
        job_name = getattr(job_func, '__name__', 'unknown_job')
        self.logger.info(f"Starting job '{job_name}' in a background thread.")
        job_thread = threading.Thread(target=job_func, args=args, kwargs=kwargs, daemon=True)
        job_thread.start()

    def _run_scheduler(self):
        """调度器线程的运行函数"""
        self.logger.info("Starting scheduler jobs...")
        
        # Price data update job (random check)
        self._run_job_in_thread(self.check_random_symbol_price)
        schedule.every(4).hours.do(self._run_job_in_thread, self.check_random_symbol_price) 

        # Daily model retraining job
        self.logger.info("Scheduling daily model retraining job at 02:00 server time.")
        schedule.every().day.at("02:00").do(self._run_job_in_thread, self.model_retrainer.run_daily_model_retraining_job)
        while not self.stop_flag.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"Scheduler error: {str(e)}", exc_info=True)
                # Avoid busy-waiting on error, but make it interruptible
                if not self.stop_flag.is_set():
                    self.stop_flag.wait(timeout=30)

# --- Model Retraining Scheduler Class ---
class ModelRetrainingScheduler:
    def __init__(self, db_path: str, logger_instance):
        self.db_path = db_path
        self.logger = logger_instance if logger_instance else get_logger("ModelRetrainingScheduler")
        self.logger.info(f"Initialized ModelRetrainingScheduler with DB: {self.db_path}")

    def get_db_connection(self):
        return sqlite3.connect(self.db_path)

    def get_active_position_tickers(self) -> list:
        """
        Fetches all unique stock symbols where users currently have a position greater than zero.
        """
        tickers = set()
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            query = """
                SELECT DISTINCT symbol
                FROM transactions
                WHERE symbol != 'CASH'
                GROUP BY account_id, symbol 
                HAVING SUM(quantity) > 1e-9;
            """
            cursor.execute(query)
            rows = cursor.fetchall()
            for row in rows:
                # Ensure symbol is a string and clean it
                symbol_val = row[0]
                if isinstance(symbol_val, str):
                    tickers.add(symbol_val.strip().replace('"', ''))
                else:
                    self.logger.warning(f"Non-string symbol found in DB: {symbol_val}")
            
            self.logger.info(f"Found {len(tickers)} unique tickers with active positions for model retraining: {list(tickers)}")
        except Exception as e:
            self.logger.error(f"Error fetching active position tickers for retraining: {e}", exc_info=True)
        finally:
            if conn:
                conn.close()
        return list(tickers)

    @log_exceptions
    def run_daily_model_retraining_job(self, ticker_symbols_to_process=None):
        self.logger.info("--- Starting Daily Model Retraining Job via Scheduler ---")
        
        if ticker_symbols_to_process is None:
            active_tickers = self.get_active_position_tickers()
        else:
            active_tickers = ticker_symbols_to_process
        
        if not active_tickers:
            self.logger.info("No active tickers found in user holdings. Skipping daily model retraining.")
            return

        self.logger.info(f"Proceeding to retrain models for {len(active_tickers)} tickers: {active_tickers}")
        
        try:
            # This function is imported from models.prediction_model.prediction_pipeline
            run_training_pipeline_for_tickers(
                ticker_symbols_to_process=active_tickers,
                force_retrain=True
            )
            self.logger.info("--- Successfully completed Daily Model Retraining Job via Scheduler ---")
        except Exception as e:
            self.logger.error(f"Daily Model Retraining Job failed: {e}", exc_info=True)
            # Don't re-raise, allow scheduler to continue other jobs

def is_market_open_today(calendar_name='NYSE'):
    """
    Check if the market for a given calendar (e.g., NYSE) is open today.
    Uses the is_session() method to avoid timezone comparison issues.
    """
    nyse = get_calendar(calendar_name)
    today_str = datetime.now().strftime('%Y-%m-%d')
    return nyse.is_session(today_str)

stock_data_manager = StockDataManager()

def start_scheduler():
    """启动调度器的便捷函数"""
    stock_data_manager.start_scheduler()

def stop_scheduler():
    """停止调度器的便捷函数"""
    stock_data_manager.stop_scheduler()

if __name__ == '__main__':
    logger = get_logger("SchedulerMain") # Logger for the __main__ block
    logger.info("Starting scheduler script directly.")
    start_scheduler() 
    try:
        while True:
            time.sleep(60)
            logger.debug("Main scheduler script thread still alive...")
    except KeyboardInterrupt:
        logger.info("Scheduler script interrupted by user. Stopping...")
    finally:
        stop_scheduler()
        logger.info("Scheduler script finished.") 