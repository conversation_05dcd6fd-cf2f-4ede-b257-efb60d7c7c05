import os
from loguru import logger
import functools
import sys

# Add at the top of the file
_IS_CONFIGURED = False
log_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs')

def configure_logger():
    """Configure logger with rotation, retention and environment-based settings"""
    global _IS_CONFIGURED
    os.makedirs(log_path, exist_ok=True)
    
    logger.remove()  # Remove default handler
    
    # Add file handler with rotation and compression
    logger.add(
        os.path.join(log_path, 'app_{time:YYYY-MM-DD}.log'),
        rotation='00:00',  # Daily rotation
        retention='30 days',
        compression='zip',
        enqueue=True,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {module}:{function}:{line} - {message}"
    )
    
    # Add console handler
    logger.add(
        sink=sys.stderr,
        level='DEBUG' if os.getenv('LOG_ENV') == 'DEVELOPMENT' else 'INFO',
        format="<green>{time:HH:mm:ss.SSS}</green> | {level.icon} | <cyan>{module}</cyan>:<cyan>{function}</cyan> - {message}"
    )
    
    _IS_CONFIGURED = True  # Mark as configured

def log_exceptions(func):
    """Decorator to automatically log exceptions"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.opt(exception=True).error(f"Exception in {func.__name__}: {str(e)}")
            raise
    return wrapper

def get_logger(name=None):
    """Get configured logger instance"""
    if not _IS_CONFIGURED:  # Use our flag instead of internal attribute
        configure_logger()
    
    # Add JSON formatting for production
    if os.getenv('LOG_ENV') == 'PRODUCTION':
        logger.add(
            sink=os.path.join(log_path, 'app.json.log'),
            serialize=True,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {module} | {function} | {line} | {message}"
        )
    
    if name:
        logger.bind(name=name)
    
    return logger
