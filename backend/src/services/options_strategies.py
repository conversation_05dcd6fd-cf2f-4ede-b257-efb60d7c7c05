"""
Options Trading Strategies Implementation

Migrated and integrated from the standalone options analysis system.
Provides all strategy analysis capabilities:
- Cash-Secured Puts analysis
- Covered Calls analysis
- Iron Condors analysis
- Win Rate Scoring algorithms
- Risk assessment and position sizing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from scipy.stats import norm
import math

from src.utils.logger import get_logger


class OptionsStrategiesAnalyzer:
    """Core options strategies analysis engine."""
    
    def __init__(self):
        self.logger = get_logger()
    
    def find_cash_secured_put_candidates(self, df: pd.DataFrame, prices: Dict[str, float],
                                       params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and filters cash-secured put candidates based on strategy parameters.
        Enhanced with progressive filtering and detailed logging for better diagnostics.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for cash-secured puts.")
            return pd.DataFrame()

        # 1. Initial Filtering for Puts
        candidates = df[df['optionType'] == 'puts'].copy()
        if candidates.empty:
            self.logger.warning("No put options found in the retrieved data.")
            return pd.DataFrame()

        self.logger.info(f"Starting with {len(candidates)} put options")

        # Add current prices
        candidates['currentPrice'] = candidates['symbol'].map(prices)
        candidates = candidates.dropna(subset=['currentPrice'])

        if candidates.empty:
            self.logger.warning("No current price data available for put options.")
            return pd.DataFrame()

        # 2. DTE Filtering with progressive approach
        min_dte = params.get('min_dte', 20)
        max_dte = params.get('max_dte', 60)
        before_dte = len(candidates)
        candidates = candidates[(candidates['dte'] >= min_dte) & (candidates['dte'] <= max_dte)]

        self.logger.info(f"After DTE filtering ({min_dte}-{max_dte} days): {len(candidates)} candidates (filtered out {before_dte - len(candidates)})")

        if candidates.empty:
            self.logger.warning(f"No put candidates found within DTE range {min_dte}-{max_dte} days.")
            return pd.DataFrame()

        # 3. Strike Price Filtering (Out-of-the-money puts)
        before_otm = len(candidates)
        candidates = candidates[candidates['strike'] < candidates['currentPrice']]

        self.logger.info(f"After OTM filtering: {len(candidates)} candidates (filtered out {before_otm - len(candidates)})")

        if candidates.empty:
            self.logger.warning("No out-of-the-money put candidates found.")
            return pd.DataFrame()

        # 4. Calculate Key Metrics
        candidates['premium'] = candidates['bid'] * 100  # Premium per contract
        candidates['maxGain'] = candidates['premium']
        candidates['maxLoss'] = (candidates['strike'] * 100) - candidates['premium']
        candidates['bufferPercent'] = (candidates['currentPrice'] - candidates['strike']) / candidates['currentPrice']

        # Enhanced Buffer percentage filtering with adaptive thresholds
        min_buffer = params.get('min_buffer_percent', 0.05)

        # For low-priced stocks (< $20), use more lenient buffer requirements
        adaptive_min_buffer = min_buffer
        for symbol in candidates['symbol'].unique():
            current_price = prices.get(symbol, 0)
            if current_price < 20:
                adaptive_min_buffer = min(min_buffer, 0.03)  # Reduce to 3% for low-priced stocks
                self.logger.info(f"Using adaptive buffer threshold {adaptive_min_buffer:.1%} for low-priced stock {symbol} (${current_price:.2f})")

        before_buffer = len(candidates)
        candidates = candidates[candidates['bufferPercent'] >= adaptive_min_buffer]

        self.logger.info(f"After buffer filtering (>= {adaptive_min_buffer:.1%}): {len(candidates)} candidates (filtered out {before_buffer - len(candidates)})")

        if candidates.empty:
            self.logger.warning(f"No put candidates found with buffer >= {adaptive_min_buffer:.1%}.")
            return pd.DataFrame()

        # 5. ROI Calculations with adaptive thresholds
        candidates['annualizedRoi'] = (candidates['bid'] / candidates['strike']) * (365 / candidates['dte'])

        # Adaptive ROI filtering - lower thresholds for conservative strategies
        min_annual_roi = params.get('min_annual_roi', 0.15)

        # For high-buffer positions, allow lower ROI requirements
        high_buffer_threshold = 0.15  # 15% buffer
        adaptive_roi_candidates = candidates.copy()

        # Apply different ROI thresholds based on buffer levels
        high_buffer_mask = candidates['bufferPercent'] >= high_buffer_threshold
        low_roi_threshold = min_annual_roi * 0.7  # 30% reduction for high-buffer positions

        roi_filter = (
            (candidates['annualizedRoi'] >= min_annual_roi) |
            ((candidates['bufferPercent'] >= high_buffer_threshold) & (candidates['annualizedRoi'] >= low_roi_threshold))
        )

        before_roi = len(candidates)
        candidates = candidates[roi_filter]

        self.logger.info(f"After adaptive ROI filtering (>= {min_annual_roi:.1%} or >= {low_roi_threshold:.1%} for high-buffer): {len(candidates)} candidates (filtered out {before_roi - len(candidates)})")

        if candidates.empty:
            self.logger.warning(f"No put candidates found meeting ROI requirements.")
            return pd.DataFrame()
        
        # 6. Risk/Reward Metrics
        candidates['riskRewardRatio'] = candidates['premium'] / candidates['maxLoss']
        candidates['returnOnRisk'] = (candidates['premium'] / (candidates['strike'] * 100)) * 100
        
        # 7. Calculate Win Rate Score
        candidates['winRateScore'] = self._calculate_put_win_rate_score(candidates)
        
        # 8. DTE Category for scoring
        candidates['dte_category'] = candidates['dte'].apply(self._categorize_dte)
        candidates['dte_score'] = candidates['dte_category'].map({
            'Optimal': 20, 'Short': 10, 'Extended': 15, 'Long': 5
        })
        
        # 9. Composite Score
        candidates['composite_score'] = (
            candidates['winRateScore'] * 0.7 +
            candidates['dte_score'] * 0.3
        )
        
        # 10. Select Best Candidates
        sort_by = params.get('sort_by', 'winRateScore')
        max_candidates = params.get('max_candidates', 10)

        if sort_by not in candidates.columns:
            sort_by = 'composite_score'

        # Select the best candidate per symbol based on scoring criteria
        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]

        # 11. Format Results
        result_cols = [
            'symbol', 'optionType', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'maxLoss', 'annualizedRoi', 'bufferPercent', 'winRateScore',
            'impliedVolatility', 'riskRewardRatio', 'returnOnRisk'
        ]

        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)
    
    def find_covered_call_candidates(self, df: pd.DataFrame, prices: Dict[str, float], 
                                   params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and filters covered call candidates based on strategy parameters.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for covered calls.")
            return pd.DataFrame()
        
        # 1. Initial Filtering for Calls
        candidates = df[df['optionType'] == 'calls'].copy()
        if candidates.empty:
            self.logger.warning("No call options found in the retrieved data.")
            return pd.DataFrame()
        
        # Add current prices
        candidates['currentPrice'] = candidates['symbol'].map(prices)
        candidates = candidates.dropna(subset=['currentPrice'])
        
        if candidates.empty:
            self.logger.warning("No current price data available for call options.")
            return pd.DataFrame()
        
        # 2. DTE Filtering
        min_dte = params.get('min_dte', 20)
        max_dte = params.get('max_dte', 60)
        candidates = candidates[(candidates['dte'] >= min_dte) & (candidates['dte'] <= max_dte)]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found within DTE range {min_dte}-{max_dte} days.")
            return pd.DataFrame()
        
        # 3. Strike Price Filtering (At or out-of-the-money calls)
        candidates = candidates[candidates['strike'] >= candidates['currentPrice']]
        
        if candidates.empty:
            self.logger.warning("No at-the-money or out-of-the-money call candidates found.")
            return pd.DataFrame()
        
        # 4. Calculate Key Metrics
        candidates['premium'] = candidates['bid'] * 100  # Premium per contract
        candidates['maxGain'] = candidates['premium']
        candidates['upsideBufferPercent'] = (candidates['strike'] - candidates['currentPrice']) / candidates['currentPrice']
        
        # Upside buffer filtering
        min_upside_buffer = params.get('min_upside_buffer', 0.02)
        candidates = candidates[candidates['upsideBufferPercent'] >= min_upside_buffer]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found with upside buffer >= {min_upside_buffer:.1%}.")
            return pd.DataFrame()
        
        # 5. ROI Calculations
        candidates['annualizedRoi'] = (candidates['bid'] / candidates['currentPrice']) * (365 / candidates['dte'])
        
        # ROI filtering
        min_annual_roi = params.get('min_annual_roi', 0.10)
        candidates = candidates[candidates['annualizedRoi'] >= min_annual_roi]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found with annualized ROI >= {min_annual_roi:.1%}.")
            return pd.DataFrame()
        
        # 6. Additional Metrics
        candidates['totalReturnIfCalled'] = candidates['upsideBufferPercent'] + (candidates['bid'] / candidates['currentPrice'])
        candidates['returnOnInvestment'] = (candidates['bid'] / candidates['currentPrice']) * 100
        candidates['opportunityCost'] = candidates['upsideBufferPercent'] * 100  # Potential gains given up
        
        # 7. Calculate Win Rate Score
        candidates['winRateScore'] = self._calculate_call_win_rate_score(candidates)
        
        # 8. Select Best Candidates
        sort_by = params.get('sort_by', 'annualizedRoi')
        max_candidates = params.get('max_candidates', 10)
        
        if sort_by not in candidates.columns:
            sort_by = 'annualizedRoi'
        
        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]
        
        # 9. Format Results
        result_cols = [
            'symbol', 'optionType', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'annualizedRoi', 'upsideBufferPercent', 'totalReturnIfCalled',
            'winRateScore', 'impliedVolatility', 'returnOnInvestment', 'opportunityCost'
        ]
        
        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)
    
    def _calculate_put_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """
        Enhanced win rate score calculation for cash-secured puts.
        """
        # 1. Enhanced Buffer Score with non-linear scaling
        buffer_score = (1 - np.exp(-df['bufferPercent'] / 10)) * 100
        
        # 2. Adaptive Time Score
        optimal_dte = 37.5
        avg_iv = df['impliedVolatility'].mean()
        if avg_iv > 0.5:  # High volatility environment
            optimal_dte = 30
        elif avg_iv < 0.25:  # Low volatility environment
            optimal_dte = 45
        
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)
        
        # 3. IV Score - prefer moderate IV levels
        iv_score = np.where(
            df['impliedVolatility'] < 0.2, 
            df['impliedVolatility'] / 0.2 * 50,  # Low IV penalty
            np.where(
                df['impliedVolatility'] <= 0.6,
                50 + (df['impliedVolatility'] - 0.2) / 0.4 * 50,  # Sweet spot
                100 - (df['impliedVolatility'] - 0.6) / 0.4 * 30   # High IV penalty
            )
        )
        
        # 4. Liquidity Score (based on bid-ask spread)
        spread = (df['ask'] - df['bid']) / ((df['ask'] + df['bid']) / 2)
        liquidity_score = np.maximum(0, 100 - spread * 1000)  # Penalize wide spreads
        
        # 5. Momentum Score (simplified - would need price history for full implementation)
        momentum_score = 50  # Neutral score as placeholder
        
        # Dynamic weightings based on market conditions
        if avg_iv > 0.5:  # High volatility - prioritize safety
            weights = {'buffer': 0.45, 'time': 0.20, 'iv': 0.15, 'liquidity': 0.15, 'momentum': 0.05}
        elif avg_iv < 0.25:  # Low volatility - can take more risk for premium
            weights = {'buffer': 0.30, 'time': 0.25, 'iv': 0.25, 'liquidity': 0.15, 'momentum': 0.05}
        else:  # Normal volatility
            weights = {'buffer': 0.35, 'time': 0.25, 'iv': 0.20, 'liquidity': 0.15, 'momentum': 0.05}
        
        # Weighted composite score
        win_rate_score = (
            buffer_score * weights['buffer'] +
            time_score * weights['time'] +
            iv_score * weights['iv'] +
            liquidity_score * weights['liquidity'] +
            momentum_score * weights['momentum']
        )
        
        return win_rate_score.round(1)

    def analyze_filtering_impact(self, df: pd.DataFrame, prices: Dict[str, float],
                               params: Dict[str, Any], strategy_type: str = 'cash_secured_puts') -> Dict[str, Any]:
        """
        Analyze the impact of each filter to help diagnose why few candidates are returned.
        Provides suggestions for parameter adjustments.
        """
        if df.empty:
            return {"error": "No data to analyze"}

        analysis = {
            "original_count": len(df),
            "filter_impact": {},
            "suggestions": [],
            "parameter_recommendations": {}
        }

        if strategy_type == 'cash_secured_puts':
            # Analyze puts filtering
            puts_df = df[df['optionType'] == 'puts'].copy()
            analysis["filter_impact"]["option_type"] = {
                "remaining": len(puts_df),
                "filtered_out": len(df) - len(puts_df)
            }

            if puts_df.empty:
                analysis["suggestions"].append("No put options available - check if symbol supports options trading")
                return analysis

            # Add current prices
            puts_df['currentPrice'] = puts_df['symbol'].map(prices)
            puts_df = puts_df.dropna(subset=['currentPrice'])

            # DTE analysis
            min_dte = params.get('min_dte', 20)
            max_dte = params.get('max_dte', 60)
            dte_filtered = puts_df[(puts_df['dte'] >= min_dte) & (puts_df['dte'] <= max_dte)]
            analysis["filter_impact"]["dte"] = {
                "remaining": len(dte_filtered),
                "filtered_out": len(puts_df) - len(dte_filtered),
                "dte_range": f"{min_dte}-{max_dte} days"
            }

            if len(dte_filtered) < len(puts_df) * 0.5:
                analysis["suggestions"].append(f"Consider expanding DTE range - current {min_dte}-{max_dte} days filters out {len(puts_df) - len(dte_filtered)} options")
                analysis["parameter_recommendations"]["dte"] = {
                    "current": f"{min_dte}-{max_dte}",
                    "suggested": f"{max(min_dte-10, 7)}-{max_dte+15}"
                }

            # OTM analysis
            otm_filtered = dte_filtered[dte_filtered['strike'] < dte_filtered['currentPrice']].copy()
            analysis["filter_impact"]["otm"] = {
                "remaining": len(otm_filtered),
                "filtered_out": len(dte_filtered) - len(otm_filtered)
            }

            if otm_filtered.empty:
                analysis["suggestions"].append("No out-of-the-money puts available - all strikes are above current price")
                return analysis

            # Buffer analysis - use .loc to avoid SettingWithCopyWarning
            otm_filtered.loc[:, 'bufferPercent'] = (otm_filtered['currentPrice'] - otm_filtered['strike']) / otm_filtered['currentPrice']
            min_buffer = params.get('min_buffer_percent', 0.05)
            buffer_filtered = otm_filtered[otm_filtered['bufferPercent'] >= min_buffer].copy()
            analysis["filter_impact"]["buffer"] = {
                "remaining": len(buffer_filtered),
                "filtered_out": len(otm_filtered) - len(buffer_filtered),
                "min_buffer": f"{min_buffer:.1%}"
            }

            if len(buffer_filtered) < len(otm_filtered) * 0.3:
                analysis["suggestions"].append(f"Buffer requirement {min_buffer:.1%} is very restrictive - consider reducing to {min_buffer*0.7:.1%}")
                analysis["parameter_recommendations"]["buffer"] = {
                    "current": f"{min_buffer:.1%}",
                    "suggested": f"{min_buffer*0.7:.1%}"
                }

            # ROI analysis - use .loc to avoid SettingWithCopyWarning
            buffer_filtered.loc[:, 'annualizedRoi'] = (buffer_filtered['bid'] / buffer_filtered['strike']) * (365 / buffer_filtered['dte'])
            min_annual_roi = params.get('min_annual_roi', 0.15)
            roi_filtered = buffer_filtered[buffer_filtered['annualizedRoi'] >= min_annual_roi]
            analysis["filter_impact"]["roi"] = {
                "remaining": len(roi_filtered),
                "filtered_out": len(buffer_filtered) - len(roi_filtered),
                "min_roi": f"{min_annual_roi:.1%}"
            }

            if len(roi_filtered) < len(buffer_filtered) * 0.3:
                analysis["suggestions"].append(f"ROI requirement {min_annual_roi:.1%} is very restrictive - consider reducing to {min_annual_roi*0.8:.1%}")
                analysis["parameter_recommendations"]["roi"] = {
                    "current": f"{min_annual_roi:.1%}",
                    "suggested": f"{min_annual_roi*0.8:.1%}"
                }

        return analysis

    def find_cash_covered_options_candidates(self, df: pd.DataFrame, prices: Dict[str, float],
                                           params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Unified analysis of both cash-secured puts and covered calls for comprehensive income strategy comparison.
        Returns detailed breakdown of both strategies with optimal recommendations per symbol.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for cash-covered options.")
            return {
                "cash_secured_puts": [],
                "covered_calls": [],
                "strategy_comparison": {},
                "analysis_metadata": {
                    "total_symbols_analyzed": 0,
                    "put_opportunities": 0,
                    "call_opportunities": 0,
                    "symbols_with_both_strategies": 0
                }
            }

        self.logger.info(f"Starting unified cash-covered options analysis for {df['symbol'].nunique()} symbols")

        # Analyze both strategies
        put_candidates = self.find_cash_secured_put_candidates(df, prices, params)
        call_candidates = self.find_covered_call_candidates(df, prices, params)

        # Create comprehensive comparison
        strategy_comparison = self._compare_strategies_by_symbol(put_candidates, call_candidates, prices)

        # Prepare detailed results
        results = {
            "cash_secured_puts": {
                "candidates": put_candidates.to_dict('records') if not put_candidates.empty else [],
                "count": len(put_candidates),
                "summary": self._summarize_strategy_results(put_candidates, "cash_secured_puts")
            },
            "covered_calls": {
                "candidates": call_candidates.to_dict('records') if not call_candidates.empty else [],
                "count": len(call_candidates),
                "summary": self._summarize_strategy_results(call_candidates, "covered_calls")
            },
            "strategy_comparison": strategy_comparison,
            "analysis_metadata": {
                "total_symbols_analyzed": df['symbol'].nunique(),
                "put_opportunities": len(put_candidates),
                "call_opportunities": len(call_candidates),
                "symbols_with_both_strategies": len([s for s in strategy_comparison.values() if s.get('both_available', False)])
            }
        }

        self.logger.info(f"Unified analysis complete: {len(put_candidates)} put opportunities, {len(call_candidates)} call opportunities")

        return results

    def _compare_strategies_by_symbol(self, put_candidates: pd.DataFrame, call_candidates: pd.DataFrame,
                                    prices: Dict[str, float]) -> Dict[str, Dict[str, Any]]:
        """Compare cash-secured puts vs covered calls for each symbol."""
        comparison = {}

        # Get all symbols from both strategies
        put_symbols = set(put_candidates['symbol'].tolist()) if not put_candidates.empty else set()
        call_symbols = set(call_candidates['symbol'].tolist()) if not call_candidates.empty else set()
        all_symbols = put_symbols.union(call_symbols)

        for symbol in all_symbols:
            symbol_comparison = {
                "symbol": symbol,
                "current_price": prices.get(symbol, 0),
                "put_available": symbol in put_symbols,
                "call_available": symbol in call_symbols,
                "both_available": symbol in put_symbols and symbol in call_symbols
            }

            # Get best candidates for each strategy
            if symbol in put_symbols:
                put_data = put_candidates[put_candidates['symbol'] == symbol].iloc[0]
                symbol_comparison["put_metrics"] = {
                    "strike": put_data['strike'],
                    "premium": put_data['premium'],
                    "annualized_roi": put_data['annualizedRoi'],
                    "buffer_percent": put_data['bufferPercent'],
                    "win_rate_score": put_data['winRateScore'],
                    "dte": put_data['dte']
                }

            if symbol in call_symbols:
                call_data = call_candidates[call_candidates['symbol'] == symbol].iloc[0]
                symbol_comparison["call_metrics"] = {
                    "strike": call_data['strike'],
                    "premium": call_data['premium'],
                    "annualized_roi": call_data['annualizedRoi'],
                    "upside_buffer_percent": call_data['upsideBufferPercent'],
                    "win_rate_score": call_data['winRateScore'],
                    "dte": call_data['dte']
                }

            # Determine optimal strategy
            if symbol_comparison["both_available"]:
                put_score = symbol_comparison["put_metrics"]["win_rate_score"]
                call_score = symbol_comparison["call_metrics"]["win_rate_score"]

                if put_score > call_score:
                    symbol_comparison["recommended_strategy"] = "cash_secured_puts"
                    symbol_comparison["score_difference"] = put_score - call_score
                else:
                    symbol_comparison["recommended_strategy"] = "covered_calls"
                    symbol_comparison["score_difference"] = call_score - put_score
            elif symbol_comparison["put_available"]:
                symbol_comparison["recommended_strategy"] = "cash_secured_puts"
                symbol_comparison["score_difference"] = 0
            elif symbol_comparison["call_available"]:
                symbol_comparison["recommended_strategy"] = "covered_calls"
                symbol_comparison["score_difference"] = 0

            comparison[symbol] = symbol_comparison

        return comparison



    def _summarize_strategy_results(self, candidates: pd.DataFrame, strategy_type: str) -> Dict[str, Any]:
        """Summarize results for a specific strategy."""
        if candidates.empty:
            return {
                "count": 0,
                "avg_roi": 0,
                "avg_win_rate_score": 0,
                "best_opportunity": None
            }

        summary = {
            "count": len(candidates),
            "avg_roi": candidates['annualizedRoi'].mean(),
            "avg_win_rate_score": candidates['winRateScore'].mean(),
            "best_opportunity": {
                "symbol": candidates.loc[candidates['winRateScore'].idxmax(), 'symbol'],
                "win_rate_score": candidates['winRateScore'].max(),
                "roi": candidates.loc[candidates['winRateScore'].idxmax(), 'annualizedRoi']
            }
        }

        if strategy_type == "cash_secured_puts":
            summary["avg_buffer_percent"] = candidates['bufferPercent'].mean()
        elif strategy_type == "covered_calls":
            summary["avg_upside_buffer"] = candidates['upsideBufferPercent'].mean()

        return summary



    def _calculate_call_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """
        Enhanced win rate score calculation for covered calls.
        """
        # 1. Upside Buffer Score
        upside_buffer_score = np.minimum(100, df['upsideBufferPercent'] * 1000)  # Cap at 100
        
        # 2. Time Score
        optimal_dte = 37.5
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)
        
        # 3. IV Score
        iv_score = np.where(
            df['impliedVolatility'] < 0.2, 
            df['impliedVolatility'] / 0.2 * 50,
            np.where(
                df['impliedVolatility'] <= 0.6,
                50 + (df['impliedVolatility'] - 0.2) / 0.4 * 50,
                100 - (df['impliedVolatility'] - 0.6) / 0.4 * 30
            )
        )
        
        # 4. Liquidity Score
        spread = (df['ask'] - df['bid']) / ((df['ask'] + df['bid']) / 2)
        liquidity_score = np.maximum(0, 100 - spread * 1000)
        
        # Weighted composite score
        win_rate_score = (
            upside_buffer_score * 0.35 +
            time_score * 0.25 +
            iv_score * 0.25 +
            liquidity_score * 0.15
        )
        
        return win_rate_score.round(1)
    
    def find_iron_condor_candidates(self, df: pd.DataFrame, prices: Dict[str, float],
                                  params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and analyzes iron condor candidates.
        Migrated from iron_condor.py with full functionality preserved.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for iron condors.")
            return pd.DataFrame()

        # Separate puts and calls
        puts_df = df[df['optionType'] == 'puts'].copy()
        calls_df = df[df['optionType'] == 'calls'].copy()

        if puts_df.empty or calls_df.empty:
            self.logger.warning("Need both puts and calls data for iron condor analysis.")
            return pd.DataFrame()

        # Add current prices
        puts_df['currentPrice'] = puts_df['symbol'].map(prices)
        calls_df['currentPrice'] = calls_df['symbol'].map(prices)

        # Filter by DTE
        min_dte = params.get('min_dte', 30)
        max_dte = params.get('max_dte', 60)
        puts_df = puts_df[(puts_df['dte'] >= min_dte) & (puts_df['dte'] <= max_dte)]
        calls_df = calls_df[(calls_df['dte'] >= min_dte) & (calls_df['dte'] <= max_dte)]

        # Estimate deltas
        puts_df['delta'] = self._estimate_put_delta(puts_df)
        calls_df['delta'] = self._estimate_call_delta(calls_df)

        iron_condors = []
        target_delta_short = params.get('target_delta_short_put', 0.20)
        min_wing_width = params.get('min_wing_width', 5)
        max_wing_width = params.get('max_wing_width', 20)
        min_annual_roi = params.get('min_annual_roi', 0.20)

        # Group by symbol and expiration
        for symbol in puts_df['symbol'].unique():
            symbol_puts = puts_df[puts_df['symbol'] == symbol]
            symbol_calls = calls_df[calls_df['symbol'] == symbol]
            current_price = prices.get(symbol)

            if not current_price:
                continue

            for exp_date in symbol_puts['expiration'].unique():
                exp_puts = symbol_puts[symbol_puts['expiration'] == exp_date]
                exp_calls = symbol_calls[symbol_calls['expiration'] == exp_date]

                if exp_puts.empty or exp_calls.empty:
                    continue

                dte = exp_puts['dte'].iloc[0]

                # Find short put (target delta around 0.20)
                short_put_candidates = exp_puts[
                    (abs(exp_puts['delta'] + target_delta_short) < 0.05) &
                    (exp_puts['strike'] < current_price)
                ]

                # Find short call (target delta around 0.20)
                short_call_candidates = exp_calls[
                    (abs(exp_calls['delta'] - target_delta_short) < 0.05) &
                    (exp_calls['strike'] > current_price)
                ]

                for _, short_put in short_put_candidates.iterrows():
                    for _, short_call in short_call_candidates.iterrows():
                        short_put_strike = short_put['strike']
                        short_call_strike = short_call['strike']

                        # Find long puts (protective)
                        long_put_candidates = exp_puts[
                            (exp_puts['strike'] < short_put_strike) &
                            (exp_puts['strike'] >= short_put_strike - max_wing_width) &
                            (exp_puts['strike'] <= short_put_strike - min_wing_width)
                        ]

                        # Find long calls (protective)
                        long_call_candidates = exp_calls[
                            (exp_calls['strike'] > short_call_strike) &
                            (exp_calls['strike'] <= short_call_strike + max_wing_width) &
                            (exp_calls['strike'] >= short_call_strike + min_wing_width)
                        ]

                        for _, long_put in long_put_candidates.iterrows():
                            for _, long_call in long_call_candidates.iterrows():
                                long_put_strike = long_put['strike']
                                long_call_strike = long_call['strike']

                                # Calculate iron condor metrics
                                put_width = short_put_strike - long_put_strike
                                call_width = long_call_strike - short_call_strike

                                # Net premium collected
                                net_premium = (
                                    short_put['bid'] + short_call['bid'] -
                                    long_put['ask'] - long_call['ask']
                                ) * 100

                                if net_premium <= 0:
                                    continue

                                # Risk and reward calculations
                                max_profit = net_premium
                                max_loss = max(put_width, call_width) * 100 - net_premium

                                if max_loss <= 0:
                                    continue

                                # Break-even points
                                lower_breakeven = short_put_strike - (net_premium / 100)
                                upper_breakeven = short_call_strike + (net_premium / 100)

                                # Probability of profit (simplified)
                                prob_of_profit = self._calculate_iron_condor_probability(
                                    current_price, lower_breakeven, upper_breakeven, dte
                                )

                                # Annualized ROI
                                annualized_roi = (max_profit / max_loss) * (365 / dte)

                                if annualized_roi < min_annual_roi:
                                    continue

                                iron_condor = {
                                    'symbol': symbol,
                                    'currentPrice': current_price,
                                    'expiration': exp_date,
                                    'dte': dte,
                                    'longPutStrike': long_put_strike,
                                    'shortPutStrike': short_put_strike,
                                    'shortCallStrike': short_call_strike,
                                    'longCallStrike': long_call_strike,
                                    'putWidth': put_width,
                                    'callWidth': call_width,
                                    'netPremium': net_premium,
                                    'maxProfit': max_profit,
                                    'maxLoss': max_loss,
                                    'annualizedRoi': annualized_roi,
                                    'lowerBreakeven': lower_breakeven,
                                    'upperBreakeven': upper_breakeven,
                                    'probabilityOfProfit': prob_of_profit,
                                    'riskRewardRatio': max_loss / max_profit,
                                    'returnOnRisk': (max_profit / max_loss) * 100,
                                }

                                iron_condors.append(iron_condor)

        if not iron_condors:
            self.logger.warning("No viable iron condor candidates found.")
            return pd.DataFrame()

        # Convert to DataFrame and calculate win rate scores
        candidates = pd.DataFrame(iron_condors)
        candidates['winRateScore'] = self._calculate_iron_condor_win_rate_score(candidates)

        # Sort and select best candidates
        sort_by = params.get('sort_by', 'winRateScore')
        max_candidates = params.get('max_candidates', 5)

        if sort_by not in candidates.columns:
            sort_by = 'annualizedRoi'

        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]

        # Format results
        result_cols = [
            'symbol', 'currentPrice', 'dte', 'longPutStrike', 'shortPutStrike',
            'shortCallStrike', 'longCallStrike', 'netPremium', 'maxProfit', 'maxLoss',
            'lowerBreakeven', 'upperBreakeven', 'probabilityOfProfit', 'annualizedRoi',
            'winRateScore'
        ]

        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)

    def _estimate_put_delta(self, puts_df: pd.DataFrame) -> pd.Series:
        """Estimate put delta using Black-Scholes approximation."""
        deltas = []
        for _, row in puts_df.iterrows():
            try:
                S = row['currentPrice']  # Current stock price
                K = row['strike']        # Strike price
                T = row['dte'] / 365.0   # Time to expiration in years
                r = 0.05                 # Risk-free rate (5% assumption)

                # Estimate volatility from implied volatility if available
                if 'impliedVolatility' in row and pd.notna(row['impliedVolatility']):
                    sigma = row['impliedVolatility']
                else:
                    # Rough estimate based on option price
                    option_price = (row['bid'] + row['ask']) / 2
                    sigma = max(0.15, min(2.0, option_price / S * 4))  # Rough heuristic

                # Black-Scholes delta calculation for puts
                if T > 0 and sigma > 0:
                    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
                    delta = norm.cdf(d1) - 1  # Put delta is negative
                else:
                    delta = -0.5  # Default estimate

                deltas.append(delta)
            except Exception:
                deltas.append(-0.5)  # Default fallback

        return pd.Series(deltas, index=puts_df.index)

    def _estimate_call_delta(self, calls_df: pd.DataFrame) -> pd.Series:
        """Estimate call delta using Black-Scholes approximation."""
        deltas = []
        for _, row in calls_df.iterrows():
            try:
                S = row['currentPrice']  # Current stock price
                K = row['strike']        # Strike price
                T = row['dte'] / 365.0   # Time to expiration in years
                r = 0.05                 # Risk-free rate (5% assumption)

                # Estimate volatility from implied volatility if available
                if 'impliedVolatility' in row and pd.notna(row['impliedVolatility']):
                    sigma = row['impliedVolatility']
                else:
                    # Rough estimate based on option price
                    option_price = (row['bid'] + row['ask']) / 2
                    sigma = max(0.15, min(2.0, option_price / S * 4))  # Rough heuristic

                # Black-Scholes delta calculation for calls
                if T > 0 and sigma > 0:
                    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
                    delta = norm.cdf(d1)  # Call delta is positive
                else:
                    delta = 0.5  # Default estimate

                deltas.append(delta)
            except Exception:
                deltas.append(0.5)  # Default fallback

        return pd.Series(deltas, index=calls_df.index)

    def _calculate_iron_condor_probability(self, current_price: float, lower_breakeven: float,
                                         upper_breakeven: float, dte: int) -> float:
        """Calculate probability of profit for iron condor (simplified)."""
        try:
            # Simplified probability calculation based on normal distribution
            # In practice, this would use more sophisticated models
            range_width = upper_breakeven - lower_breakeven
            price_range = range_width / current_price

            # Rough estimate: wider range = higher probability
            # This is a simplified heuristic
            base_prob = min(0.8, price_range * 2)

            # Adjust for time decay (longer time = lower probability)
            time_factor = max(0.3, 1 - (dte / 365) * 0.5)

            return base_prob * time_factor
        except Exception:
            return 0.5  # Default fallback

    def _calculate_iron_condor_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """Calculate composite win rate score for iron condors."""
        # 1. Probability of Profit Score (40% weight)
        prob_score = df['probabilityOfProfit'] * 100

        # 2. Return on Risk Score (30% weight)
        ror_score = (df['returnOnRisk'].clip(0, 50) / 50) * 100

        # 3. Time Score (15% weight) - 30-45 DTE is optimal
        optimal_dte = 37.5
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)

        # 4. Balance Score (15% weight) - prefer balanced wings
        wing_balance = abs(df['putWidth'] - df['callWidth']) / ((df['putWidth'] + df['callWidth']) / 2)
        balance_score = np.maximum(0, 100 - wing_balance * 100)

        # Weighted composite score
        win_rate_score = (
            prob_score * 0.40 +
            ror_score * 0.30 +
            time_score * 0.15 +
            balance_score * 0.15
        )

        return win_rate_score.round(1)

    def _categorize_dte(self, dte: int) -> str:
        """Categorize DTE for scoring purposes."""
        if dte < 22:
            return 'Short'
        elif dte <= 35:
            return 'Optimal'
        elif dte <= 50:
            return 'Extended'
        else:
            return 'Long'
