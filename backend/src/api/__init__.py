from flask import Blueprint
import sqlite3
import os

def get_db_connection():
    # 使用绝对路径 - updated for new structure
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'stock_trading.db')
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

# 创建蓝图
accounts_bp = Blueprint('accounts', __name__, url_prefix='/api/accounts')
transactions_bp = Blueprint('transactions', __name__, url_prefix='/api/transactions')
analytics_bp = Blueprint('analytics', __name__, url_prefix='/api/analytics')
market_bp = Blueprint('market', __name__, url_prefix='/api/market')
strategies_bp = Blueprint('strategies', __name__, url_prefix='/api/strategies')

# 导入路由
from . import accounts
from . import transactions
from . import analytics
from . import market 
from . import strategies