from flask import jsonify, request
from datetime import datetime, timedelta
import pandas as pd
from . import analytics_bp, get_db_connection
from ..utils.calculations import get_top_stocks, calculate_current_market_value
import traceback
import numpy as np
from ..utils.logger import get_logger

logger = get_logger()

@analytics_bp.route('/overview', strict_slashes=False)
def get_overview():
    account_id_filter = request.args.get('account_id')
    conn = get_db_connection()
    try:
        accounts_query = 'SELECT account_id, name FROM accounts'
        query_params = []
        if account_id_filter:
            accounts_query += ' WHERE account_id = ?'
            query_params.append(int(account_id_filter))
        
        accounts_df = pd.read_sql(accounts_query, conn, params=query_params if query_params else None)

        if accounts_df.empty:
            return jsonify({'total_return': 0, 'account_breakdown': []})

        realized_gains_query = 'SELECT account_id, SUM(realized_gain) as total_realized_gains FROM realized_gains GROUP BY account_id'
        realized_gains_df_all = pd.read_sql(realized_gains_query, conn)
        realized_gains_map = realized_gains_df_all.set_index('account_id')['total_realized_gains'].to_dict()

        account_breakdown = []
        for _, account_row in accounts_df.iterrows():
            current_acc_id = int(account_row['account_id'])
            account_name = account_row['name']

            acc_stock_mv, acc_stock_cb, _ = calculate_current_market_value(conn, account_id=current_acc_id)
            
            acc_stock_mv = acc_stock_mv or 0.0
            acc_stock_cb = acc_stock_cb or 0.0

            acc_unrealized_gains = acc_stock_mv - acc_stock_cb
            
            acc_realized_gains_db = realized_gains_map.get(current_acc_id, 0.0)
            
            current_total_gains = acc_unrealized_gains + acc_realized_gains_db
            
            acc_return_rate = (acc_unrealized_gains / acc_stock_cb) if acc_stock_cb > 0 else 0.0

            account_data = {
                'account_id': current_acc_id,
                'name': account_name,
                'current_value': round(acc_stock_mv, 2),
                'realized_gains': round(acc_realized_gains_db, 2),
                'total_gains': round(current_total_gains, 2),
                'return_rate': acc_return_rate,
                'top_stocks': get_top_stocks(conn, current_acc_id)
            }
            account_breakdown.append(account_data)

        valid_returns = [acc['return_rate'] for acc in account_breakdown if acc['return_rate'] is not None]
        overall_total_return = sum(valid_returns) / len(valid_returns) if valid_returns else 0.0
        
        response = {
            'total_return': overall_total_return,
            'account_breakdown': account_breakdown
        }
        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in get_overview: {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@analytics_bp.route('/holdings', strict_slashes=False)
def get_holdings():
    account_id = request.args.get('account_id')
    conn = get_db_connection()
    try:
        # 构建基础查询条件
        where_clause = "WHERE t.symbol != 'CASH'"
        params = []
        if account_id:
            where_clause += " AND t.account_id = ?"
            params = [account_id]  # 为每个 CTE 添加一个参数
            params.extend([account_id] * 2)  # 为其他两个 CTE 添加参数

        # 获取当前持仓和收益数据
        query = f'''
            WITH current_prices AS (
                SELECT symbol, close as current_price
                FROM cached_prices
                WHERE date = (SELECT MAX(date) FROM cached_prices)
            ),
            transactions_with_running_quantity AS (
                SELECT
                    t.account_id,
                    t.symbol,
                    t.trans_time,
                    t.quantity,
                    t.price,
                    SUM(t.quantity) OVER (PARTITION BY t.account_id, t.symbol ORDER BY t.trans_time, t.transaction_id) as running_quantity
                FROM transactions t
                {where_clause}
            ),
            last_zero_crossing AS (
                SELECT
                    account_id,
                    symbol,
                    MAX(trans_time) as last_zero_time
                FROM transactions_with_running_quantity
                WHERE running_quantity <= 0
                GROUP BY account_id, symbol
            ),
            relevant_buy_transactions AS (
                SELECT 
                    t.account_id,
                    t.symbol,
                    SUM(CASE WHEN t.quantity > 0 THEN t.quantity ELSE 0 END) as relevant_buy_quantity,
                    SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END) as relevant_total_cost
                FROM transactions t
                LEFT JOIN last_zero_crossing lzc 
                    ON t.account_id = lzc.account_id AND t.symbol = lzc.symbol
                {where_clause} 
                    AND t.quantity > 0 
                    AND t.trans_time > COALESCE(lzc.last_zero_time, '1900-01-01 00:00:00') -- Only buys after the last zero crossing
                GROUP BY t.account_id, t.symbol
            ),
            current_holdings_calc AS (
                SELECT 
                    t.account_id,
                    t.symbol,
                    SUM(t.quantity) as current_quantity
                FROM transactions t
                {where_clause}
                GROUP BY t.account_id, t.symbol
            ),
            current_holdings AS (
                SELECT 
                    chc.account_id,
                    chc.symbol,
                    chc.current_quantity,
                    COALESCE(rbt.relevant_buy_quantity, 0) as relevant_buy_quantity,
                    COALESCE(rbt.relevant_total_cost, 0) as relevant_total_cost,
                    cp.current_price,
                    CASE 
                        WHEN COALESCE(rbt.relevant_buy_quantity, 0) > 0 THEN
                            -- Calculate avg cost based on buys since last zero crossing
                            rbt.relevant_total_cost / rbt.relevant_buy_quantity
                        ELSE 0 -- Or handle case where no relevant buys found (e.g., started with short?)
                    END as avg_cost
                FROM current_holdings_calc chc
                LEFT JOIN relevant_buy_transactions rbt 
                    ON chc.account_id = rbt.account_id AND chc.symbol = rbt.symbol
                LEFT JOIN current_prices cp ON chc.symbol = cp.symbol
                WHERE chc.current_quantity > 0
            )
            SELECT 
                ch.account_id,
                a.name,
                ch.symbol,
                ch.current_quantity as quantity,
                ch.avg_cost as cost_per_share,
                ch.current_quantity * ch.avg_cost as cost,
                ch.current_price,
                (ch.current_quantity * ch.current_price) as value,
                (ch.current_quantity * ch.current_price - (ch.current_quantity * ch.avg_cost)) as gains,
                CASE 
                    WHEN (ch.current_quantity * ch.avg_cost) > 0 THEN
                        ((ch.current_quantity * ch.current_price) - (ch.current_quantity * ch.avg_cost)) / (ch.current_quantity * ch.avg_cost)
                    ELSE 0 
                END as return_rate
            FROM current_holdings ch
            LEFT JOIN accounts a ON ch.account_id = a.account_id
            ORDER BY value DESC
        '''

        # Adjust params if account_id is provided
        if account_id:
            # Parameter count needs to match placeholders in the final query
            # Placeholders exist in:
            # 1. transactions_with_running_quantity (copied from where_clause)
            # 2. relevant_buy_transactions (copied from where_clause)
            # 3. current_holdings_calc (copied from where_clause)
            # So, if where_clause has 1 '?', we need 3 params.
            param_count = where_clause.count('?')
            params = [account_id] * param_count * 3 

        holdings = conn.execute(query, params).fetchall()

        holdings_data = [{
            'account_name': row['name'],
            'symbol': row['symbol'],
            'quantity': row['quantity'],
            'current_price': row['current_price'],
            'cost_per_share': row['cost_per_share'],
            'cost': row['cost'],
            'value': row['value'],
            'gains': row['gains'],
            'return_rate': row['return_rate']
        } for row in holdings]

        return jsonify({'holdings': holdings_data})

    except Exception as e:
        logger.error(f"Error in get_holdings: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@analytics_bp.route('/realized_gains')
def get_realized_gains():
    """获取特定账户的已实现收益详情"""
    account_id = request.args.get('account_id', type=int)
    if account_id is None:
        return jsonify({'error': 'Missing account_id parameter'}), 400
        
    conn = None
    try:
        conn = get_db_connection()

        # Query realized gains for the specific account
        cursor = conn.execute('''
            SELECT
                symbol,
                buy_date,
                sell_date,
                buy_quantity,
                buy_price,
                sell_price,
                realized_gain
            FROM realized_gains
            WHERE account_id = ?
            ORDER BY sell_date DESC, symbol
        ''', [account_id])

        gains_data = cursor.fetchall()

        # Convert to list of dictionaries for JSON response
        realized_gains = []
        for row in gains_data:
            realized_gains.append({
                'symbol': row['symbol'],
                'buy_date': row['buy_date'],
                'sell_date': row['sell_date'],
                'buy_quantity': float(row['buy_quantity']),
                'buy_price': float(row['buy_price']),
                'sell_price': float(row['sell_price']),
                'realized_gain': float(row['realized_gain'])
            })

        return jsonify({
            'account_id': account_id,
            'realized_gains': realized_gains
        })

    except Exception as e:
        logger.error(f"Error getting realized gains: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()