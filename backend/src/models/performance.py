import pandas as pd
import numpy as np
from typing import Dict, Optional
from datetime import datetime, timedelta
from ..utils.logger import get_logger

logger = get_logger()

class PerformanceMonitor:
    """Enhanced performance monitoring and analysis"""
    def __init__(self):
        self.trades = []
        self.daily_returns = pd.Series()
        self.portfolio_values = pd.Series()
        self.benchmark_returns = pd.Series()
        self.metrics = {
            'annual_return': 0.0,      # 年化收益率：投资组合的年度回报率
            'sharpe_ratio': 0.0,       # 夏普比率：超额收益与波动率的比值，衡量风险调整后收益
            'sortino_ratio': 0.0,      # 索提诺比率：只考虑下行波动率的风险调整收益指标
            'max_drawdown': 0.0,       # 最大回撤：历史最大的价值下跌幅度
            'win_rate': 0.0,           # 胜率：盈利交易占总交易的比例
            'profit_factor': 0.0,      # 盈亏比：总盈利与总亏损的比值
            'turnover': 0.0,           # 换手率：交易量与组合价值的比率
            'total_trades': 0,         # 总交易次数
            'avg_trade_duration': 'N/A',  # 平均持仓时间
            'volatility': 0.0,         # 波动率：收益率的标准差
            'alpha': 0.0,              # 阿尔法：相对基准的超额收益
            'beta': 0.0,               # 贝塔：相对市场的系统性风险
            'information_ratio': 0.0,   # 信息比率：超额收益与跟踪误差的比值
            'calmar_ratio': 0.0        # 卡玛比率：年化收益与最大回撤的比值
        }
        
    def update(self, trade: Dict, portfolio_value: float, benchmark_return: float = None):
        """Update performance metrics with new trade and benchmark data"""
        try:
            self.trades.append(trade)
            current_time = pd.Timestamp.now()
            
            # Update portfolio values
            self.portfolio_values[current_time] = portfolio_value
            
            # Update benchmark returns if provided
            if benchmark_return is not None:
                self.benchmark_returns[current_time] = benchmark_return
            
            # Calculate metrics if we have enough data
            if len(self.portfolio_values) >= 2:
                self._calculate_metrics()
                
        except Exception as e:
            logger.error(f"Error updating performance metrics: {str(e)}")
    
    def _calculate_metrics(self):
        """Calculate comprehensive performance metrics"""
        try:
            if len(self.portfolio_values) < 2:
                return
            
            # Calculate returns
            self.daily_returns = self.portfolio_values.pct_change().dropna()
            
            # Basic metrics
            total_trades = len(self.trades)
            winning_trades = sum(1 for t in self.trades if t.get('profit', 0) > 0)
            
            # Calculate advanced metrics
            annual_return = self._calculate_annual_return()
            sharpe = self._calculate_sharpe_ratio()
            sortino = self._calculate_sortino_ratio()
            max_dd = self._calculate_max_drawdown()
            
            # Calculate alpha and beta if benchmark data available
            alpha, beta, info_ratio = self._calculate_risk_metrics()
            
            # Update metrics dictionary
            self.metrics.update({
                'annual_return': round(annual_return * 100, 2),
                'sharpe_ratio': round(sharpe, 2),
                'sortino_ratio': round(sortino, 2),
                'max_drawdown': round(max_dd * 100, 2),
                'win_rate': round((winning_trades / total_trades * 100 if total_trades > 0 else 0), 2),
                'profit_factor': round(self._calculate_profit_factor(), 2),
                'turnover': round(self._calculate_turnover() * 100, 2),
                'total_trades': total_trades,
                'avg_trade_duration': self._calculate_avg_trade_duration(),
                'volatility': round(self.daily_returns.std() * np.sqrt(252) * 100, 2) if len(self.daily_returns) > 0 else 0.0,
                'alpha': round(alpha * 100, 2),
                'beta': round(beta, 2),
                'information_ratio': round(info_ratio, 2),
                'calmar_ratio': round(annual_return / abs(max_dd) if abs(max_dd) > 0 else 0, 2)
            })
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
    
    def _calculate_annual_return(self) -> float:
        """Calculate annualized return"""
        try:
            if len(self.portfolio_values) < 2:
                return 0.0
                
            start_value = self.portfolio_values.iloc[0]
            end_value = self.portfolio_values.iloc[-1]
            days = (self.portfolio_values.index[-1] - self.portfolio_values.index[0]).days
            
            if days == 0 or start_value == 0:
                return 0.0
                
            total_return = (end_value / start_value) - 1
            annual_return = (1 + total_return) ** (365.0 / days) - 1
            
            return annual_return
            
        except Exception as e:
            logger.error(f"Error calculating annual return: {str(e)}")
            return 0.0
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(self.daily_returns) < 30:
                return 0.0
                
            rf_rate = 0.02  # Assume 2% risk-free rate
            excess_returns = self.daily_returns - rf_rate/252
            
            if len(excess_returns) == 0:
                return 0.0
                
            volatility = self.daily_returns.std() * np.sqrt(252)
            
            if volatility == 0:
                return 0.0
                
            sharpe = (excess_returns.mean() * 252) / volatility
            
            return sharpe
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return 0.0
    
    def _calculate_sortino_ratio(self) -> float:
        """Calculate Sortino ratio using downside deviation"""
        try:
            if len(self.daily_returns) < 30:
                return 0.0
                
            rf_rate = 0.02  # Assume 2% risk-free rate
            excess_returns = self.daily_returns - rf_rate/252
            downside_returns = excess_returns[excess_returns < 0]
            
            if len(downside_returns) == 0:
                return 0.0
                
            downside_std = np.sqrt(np.mean(downside_returns**2)) * np.sqrt(252)
            
            if downside_std == 0:
                return 0.0
                
            return (excess_returns.mean() * 252) / downside_std
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {str(e)}")
            return 0.0
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        try:
            if len(self.portfolio_values) < 2:
                return 0.0
                
            rolling_max = self.portfolio_values.expanding().max()
            drawdowns = self.portfolio_values / rolling_max - 1
            max_drawdown = drawdowns.min()
            
            return max_drawdown
            
        except Exception as e:
            logger.error(f"Error calculating maximum drawdown: {str(e)}")
            return 0.0
    
    def _calculate_risk_metrics(self) -> tuple:
        """Calculate alpha, beta and information ratio"""
        try:
            if len(self.benchmark_returns) < 30:
                return 0.0, 1.0, 0.0
                
            # Calculate beta
            covar = np.cov(self.daily_returns, self.benchmark_returns)[0][1]
            var = np.var(self.benchmark_returns)
            beta = covar / var if var != 0 else 1.0
            
            # Calculate alpha (annualized)
            rf_rate = 0.02  # Assume 2% risk-free rate
            alpha = (self.daily_returns.mean() - rf_rate/252) - (beta * (self.benchmark_returns.mean() - rf_rate/252))
            alpha = alpha * 252
            
            # Calculate information ratio
            tracking_error = (self.daily_returns - self.benchmark_returns).std() * np.sqrt(252)
            info_ratio = alpha / tracking_error if tracking_error > 0 else 0
            
            return alpha, beta, info_ratio
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {str(e)}")
            return 0.0, 1.0, 0.0
    
    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor"""
        try:
            profits = sum(t['profit'] for t in self.trades if t.get('profit', 0) > 0)
            losses = abs(sum(t['profit'] for t in self.trades if t.get('profit', 0) < 0))
            
            return profits / losses if losses != 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating profit factor: {str(e)}")
            return 0.0
    
    def _calculate_turnover(self) -> float:
        """Calculate portfolio turnover"""
        try:
            if len(self.trades) < 2:
                return 0.0
                
            total_value = self.portfolio_values.mean()
            if total_value == 0:
                return 0.0
                
            total_volume = sum(abs(t.get('value', 0)) for t in self.trades)
            turnover = total_volume / (2 * total_value)  # Divide by 2 to account for round trips
            
            return turnover
            
        except Exception as e:
            logger.error(f"Error calculating turnover: {str(e)}")
            return 0.0
    
    def _calculate_avg_trade_duration(self) -> str:
        """Calculate average trade duration"""
        try:
            if not self.trades:
                return "N/A"
                
            durations = []
            for trade in self.trades:
                if trade.get('entry_time') and trade.get('exit_time'):
                    duration = trade['exit_time'] - trade['entry_time']
                    durations.append(duration)
            
            if not durations:
                return "N/A"
                
            avg_duration = sum(durations, timedelta()) / len(durations)
            
            # Format duration
            days = avg_duration.days
            hours = avg_duration.seconds // 3600
            minutes = (avg_duration.seconds % 3600) // 60
            
            if days > 0:
                return f"{days}d {hours}h"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
                
        except Exception as e:
            logger.error(f"Error calculating average trade duration: {str(e)}")
            return "N/A"
    
    def generate_report(self) -> str:
        """Generate comprehensive performance report"""
        try:
            if len(self.portfolio_values) < 2:
                return "Insufficient data for performance report"
                
            report = [
                "=== Strategy Performance Report ===",
                f"Period: {self.portfolio_values.index[0].strftime('%Y-%m-%d')} to {self.portfolio_values.index[-1].strftime('%Y-%m-%d')}",
                "",
                "Returns and Risk Metrics:",
                f"Annual Return: {self.metrics['annual_return']}%",
                f"Volatility: {self.metrics['volatility']}%",
                f"Sharpe Ratio: {self.metrics['sharpe_ratio']}",
                f"Sortino Ratio: {self.metrics['sortino_ratio']}",
                f"Maximum Drawdown: {self.metrics['max_drawdown']}%",
                f"Calmar Ratio: {self.metrics['calmar_ratio']}",
                "",
                "Risk-Adjusted Metrics:",
                f"Alpha: {self.metrics['alpha']}%",
                f"Beta: {self.metrics['beta']}",
                f"Information Ratio: {self.metrics['information_ratio']}",
                "",
                "Trading Statistics:",
                f"Win Rate: {self.metrics['win_rate']}%",
                f"Profit Factor: {self.metrics['profit_factor']}",
                f"Total Trades: {self.metrics['total_trades']}",
                f"Average Trade Duration: {self.metrics['avg_trade_duration']}",
                f"Portfolio Turnover: {self.metrics['turnover']}%"
            ]
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"Error generating performance report: {str(e)}")
            return "Error generating performance report" 