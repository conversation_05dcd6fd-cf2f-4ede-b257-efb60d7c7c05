import pandas as pd
import numpy as np
import logging
from prophet import Prophet
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, List, Tuple
from datetime import datetime, timedelta
from collections import defaultdict
from functools import cached_property
from ..utils.logger import get_logger

logging.basicConfig(level=logging.INFO)
logger = get_logger()

@dataclass
class Stock:
    """Represents a stock position with entry information and risk metrics"""
    symbol: str
    shares: float
    avg_price: float
    entry_date: datetime = None
    volatility: float = 0.0
    beta: float = 1.0
    last_updated: datetime = None
    position_type: str = "LONG"  # 或 "SHORT"
    current_price: Optional[float] = None
    
    def __post_init__(self):
        if self.entry_date is None:
            self.entry_date = datetime.now()
        if not isinstance(self.shares, (int, float)) or self.shares <= 0:
            raise ValueError("Shares must be positive number")
        self.unrealized_pl = 0.0  # 未实现盈亏
        self.unrealized_pl_pct = 0.0  # 未实现盈亏百分比
    
    @property
    def current_value(self) -> float:
        """当前持仓市值"""
        if self.current_price is not None:
            value = self.shares * self.current_price
            return -value if self.position_type == "SHORT" else value
        else:
            logger.warning(f"Cannot calculate current_value for {self.symbol} as current_price is None.")
            # Fallback: return cost basis value
            cost_basis = self.shares * self.avg_price
            return -cost_basis if self.position_type == "SHORT" else cost_basis
    
    @property
    def unrealized_pnl(self) -> float:
        """未实现盈亏，与unrealized_pl保持一致"""
        return self.unrealized_pl
    
    @property
    def position_value(self) -> float:
        """Calculate current position value"""
        value = self.shares * self.avg_price
        return -value if self.position_type == "SHORT" else value
    
    @property
    def days_held(self) -> int:
        """Calculate number of days position has been held"""
        return (datetime.now() - self.entry_date).days
    
    @property
    def risk_contribution(self) -> float:
        """Calculate position's risk contribution"""
        return self.position_value * self.volatility * self.beta
    
    def update_risk_metrics(self, current_price: float, market_data: pd.DataFrame):
        if self.last_updated and (datetime.now() - self.last_updated) < timedelta(hours=1):
            return  # 1小时内不重复计算
        
        # Store the current price used for this update
        self.current_price = current_price 
        
        if market_data.empty or 'Close' not in market_data.columns:
            # Still update PnL even if market_data is invalid for vol/beta
            logger.warning(f"Invalid market data for {self.symbol}, only updating PnL.")
        else:
             if len(market_data) < 2:
                 logger.warning(f"Insufficient market data points for {self.symbol}, only updating PnL.")
             else:
                 try:
                     # Update volatility using recent price data
                     returns = market_data['Close'].pct_change()
                     self.volatility = returns.std() * np.sqrt(252)
                     
                     # Update beta if market data is available
                     if 'Market_Return' in market_data.columns:
                         market_returns = market_data['Market_Return']
                         window_size = min(90, len(market_data))  # 3个月滚动窗口
                         returns_rolling = returns.rolling(window=window_size).mean().dropna()
                         market_returns_rolling = market_returns.rolling(window=window_size).mean().dropna()
                         # Align indices after rolling mean calculation
                         common_index = returns_rolling.index.intersection(market_returns_rolling.index)
                         if len(common_index) > 1: # Check if enough data remains after alignment for cov
                             covariance = returns_rolling.loc[common_index].cov(market_returns_rolling.loc[common_index])
                             market_variance = market_returns_rolling.loc[common_index].var()
                             self.beta = covariance / market_variance if market_variance != 0 else 1.0
                         else:
                             logger.warning(f"Not enough overlapping data after rolling/alignment to calculate beta for {self.symbol}.")
                             self.beta = 1.0 # Default beta if calculation fails
                     else:
                         self.beta = 1.0 # Default if no market return data
                 
                 except Exception as e_risk:
                      logger.error(f"Error calculating vol/beta for {self.symbol}: {e_risk}")
                      # Keep default vol/beta if calculation fails

        # Always calculate unrealized P&L based on the *passed* current_price
        try:
            if self.avg_price is not None and self.shares is not None:
                cost_basis = self.avg_price * self.shares
                # Calculate PnL based on position type
                if self.position_type == "SHORT":
                    self.unrealized_pl = (self.avg_price - current_price) * self.shares
                else: # Default to LONG
                    self.unrealized_pl = (current_price - self.avg_price) * self.shares
                
                self.unrealized_pl_pct = (self.unrealized_pl / cost_basis) if cost_basis != 0 else 0
            else:
                 logger.warning(f"Cannot calculate PnL for {self.symbol} due to missing avg_price or shares.")
                 self.unrealized_pl = 0.0
                 self.unrealized_pl_pct = 0.0

        except Exception as e_pnl_calc:
            logger.error(f"Error calculating PnL values for {self.symbol}: {e_pnl_calc}")
            self.unrealized_pl = 0.0
            self.unrealized_pl_pct = 0.0

        self.last_updated = datetime.now()

    @cached_property
    def historical_volatility(self):
        return self._calculate_historical_volatility()
    
    def _calculate_historical_volatility(self, window: int = 90) -> float:
        """计算年化历史波动率（滚动窗口）"""
        try:
            if not hasattr(self, '_historical_data') or self._historical_data.empty:
                raise ValueError("No historical data available")
            
            if len(self._historical_data) < 2:
                logger.warning(f"Insufficient data points for {self.symbol}")
                return 0.0
                
            returns = self._historical_data['Close'].pct_change().dropna()
            if len(returns) < window:
                logger.warning(f"Not enough data for {window}-day window on {self.symbol}")
                window = len(returns)
            
            rolling_returns = returns.rolling(window=window)
            return rolling_returns.std().iloc[-1] * np.sqrt(252)  # 年化波动率
        
        except Exception as e:
            logger.error(f"Volatility calculation failed for {self.symbol}: {str(e)}")
            return 0.0

    @property
    def quantity(self) -> float:
        """Alias for shares"""
        return self.shares

    @staticmethod
    def portfolio_risk_metrics(stocks: List['Stock']) -> Dict[str, float]:
        """计算组合层面的风险指标"""
        if not stocks:
            return {
                "portfolio_beta": 0.0,
                "portfolio_volatility": 0.0,
                "concentration_risk": 0.0,
                "value_at_risk": 0.0
            }
            
        try:
            total_value = sum(abs(s.position_value) for s in stocks)  # 考虑做空
            if total_value <= 0:
                raise ValueError("Total portfolio value must be positive")
            
            # 基础指标
            beta = sum(s.beta * s.position_value for s in stocks) / total_value
            volatility = sum(s.volatility * abs(s.position_value) for s in stocks) / total_value
            concentration = max(abs(s.position_value) / total_value for s in stocks)
            
            # 风险价值（简化计算）
            var = volatility * 1.645 * np.sqrt(10/252)  # 95%置信度，10天持有期
            
            return {
                "portfolio_beta": round(beta, 4),
                "portfolio_volatility": round(volatility, 4),
                "concentration_risk": round(concentration, 4),
                "value_at_risk": round(var, 4)
            }
            
        except ZeroDivisionError:
            logger.error("Cannot calculate risk metrics for zero-value portfolio")
            return {k: 0.0 for k in ["portfolio_beta", "portfolio_volatility", "concentration_risk", "value_at_risk"]}
        except Exception as e:
            logger.error(f"Risk metrics calculation failed: {str(e)}")
            return {k: 0.0 for k in ["portfolio_beta", "portfolio_volatility", "concentration_risk", "value_at_risk"]} 
        