#!/bin/bash

# Stock Trading Application Development Server Startup Script
# This script starts both the Flask backend and React frontend development servers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5001
FRONTEND_PORT=5173
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
VENV_DIR="venv"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to kill processes on specific ports
cleanup_ports() {
    print_status "Cleaning up processes on ports $BACKEND_PORT and $FRONTEND_PORT..."
    
    # Kill backend process
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Killing process on port $BACKEND_PORT"
        lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill frontend process
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "Killing process on port $FRONTEND_PORT"
        lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    sleep 2
}

# Function to handle script termination
cleanup() {
    print_status "Shutting down servers..."
    cleanup_ports
    print_success "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists python3; then
    print_error "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js 16 or higher."
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

print_success "All prerequisites are available"

# Check if we're in the correct directory
if [[ ! -d "$BACKEND_DIR" ]] || [[ ! -d "$FRONTEND_DIR" ]]; then
    print_error "Please run this script from the project root directory"
    print_error "Expected directories: $BACKEND_DIR/ and $FRONTEND_DIR/"
    exit 1
fi

# Clean up any existing processes
cleanup_ports

# Check if virtual environment exists
if [[ ! -d "$VENV_DIR" ]]; then
    print_warning "Virtual environment not found. Creating one..."
    python3 -m venv "$VENV_DIR"
    print_success "Virtual environment created"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

# Check if backend dependencies are installed
uv pip install -r requirements.txt

# Check if frontend dependencies are installed
if [[ ! -d "$FRONTEND_DIR/node_modules" ]]; then
    print_warning "Frontend dependencies not found. Installing..."
    cd "$FRONTEND_DIR"
    npm install
    cd ..
    print_success "Frontend dependencies installed"
fi

# Check if database exists
if [[ ! -f "$BACKEND_DIR/data/stock_trading.db" ]]; then
    print_warning "Database not found. Initializing..."
    mkdir -p "$BACKEND_DIR/data"
    sqlite3 "$BACKEND_DIR/data/stock_trading.db" < "$BACKEND_DIR/config/schema.sql"
    print_success "Database initialized"
fi

# Start backend server
print_status "Starting Flask backend server on port $BACKEND_PORT..."
cd "$BACKEND_DIR"
export FLASK_ENV=development
export FLASK_DEBUG=1
python -m src.app &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_error "Failed to start backend server"
    exit 1
fi

print_success "Backend server started (PID: $BACKEND_PID)"

# Start frontend server
print_status "Starting React frontend server on port $FRONTEND_PORT..."
cd "$FRONTEND_DIR"
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait a moment for frontend to start
sleep 3

# Check if frontend started successfully
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_error "Failed to start frontend server"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

print_success "Frontend server started (PID: $FRONTEND_PID)"

# Display startup information
echo ""
echo "=========================================="
echo -e "${GREEN}🚀 Stock Trading Application Started!${NC}"
echo "=========================================="
echo -e "📊 Frontend: ${BLUE}http://localhost:$FRONTEND_PORT${NC}"
echo -e "🔧 Backend:  ${BLUE}http://localhost:$BACKEND_PORT${NC}"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop both servers${NC}"
echo "=========================================="
echo ""

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
